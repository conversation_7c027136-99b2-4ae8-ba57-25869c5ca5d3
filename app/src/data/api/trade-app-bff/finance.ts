import { createApiClient } from 'src/data/apiClient';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { config } from 'src/config';
import { getAccessToken } from 'src/auth/utils/authSession';
import {
  Account,
  accountSchema,
} from 'src/data/schemas/api/trade-app-bff/finance/Account';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import {
  PaymentMethod,
  paymentMethodSchema,
  ZuoraStatus,
  zuoraStatusSchema,
} from 'src/data/schemas/api/trade-app-bff/finance/PaymentMethod';
import { AxiosResponse } from 'axios';

class FinanceTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getAccount(companyId: number): Promise<AxiosResponse<Account>> {
    const response = await this.client.get('/zuora/account', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(accountSchema, response.data),
    };
  }

  public async getPaymentMethod(
    paymentMethodId: string,
    companyId: number,
  ): Promise<AxiosResponse<PaymentMethod>> {
    const response = await this.client.get(
      '/zuora/payment-methods/{paymentMethodId}',
      {
        params: {
          path: { paymentMethodId },
          header: { 'x-trade-company-id': companyId },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(paymentMethodSchema, response.data),
    };
  }

  public async getZuoraStatus(
    companyId: number,
  ): Promise<AxiosResponse<ZuoraStatus>> {
    const response = await this.client.get('/zuora/status', {
      params: {
        header: { 'x-trade-company-id': companyId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(zuoraStatusSchema, response.data),
    };
  }

  public async updateAccount(
    companyId: number,
    accountId: string,
    paymentMethodId: string,
  ): Promise<AxiosResponse<Account>> {
    const response = await this.client.patch('/zuora/account/{accountId}', {
      params: {
        path: { accountId },
        header: { 'x-trade-company-id': companyId },
      },
      body: {
        paymentMethodId,
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(accountSchema, response.data),
    };
  }
}

export const financeTradeBffApi = new FinanceTradeBffApi();
