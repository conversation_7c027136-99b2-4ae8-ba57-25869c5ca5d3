import { config } from 'src/config';
import { paths } from 'src/data/api-specs/trade-app-bff';
import { createApiClient } from 'src/data/apiClient';
import { getAccessToken } from 'src/auth/utils/authSession';
import { AxiosResponse } from 'axios';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import {
  SecureContactsOptionsResponse,
  secureContactsOptionsResponseSchema,
  Assigned,
  assignedSchema,
  newAssignedSchema,
  NewAssigned,
} from '../../schemas/api/trade-app-bff/secure-contacts';

class SecureContactsTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getSecureContactsOptions(
    companyId: number,
  ): Promise<AxiosResponse<SecureContactsOptionsResponse>> {
    const response = await this.client.get('/secure-contacts/options', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        secureContactsOptionsResponseSchema,
        response.data,
      ),
    };
  }

  public async getAssignedSecureContact(
    companyId: number,
  ): Promise<AxiosResponse<Assigned>> {
    const response = await this.client.get('/secure-contacts/assigned', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(assignedSchema, response.data),
    };
  }

  public async updateAssignedSecureContact(
    companyId: number,
    secureContactId: string,
    contactId: string,
  ): Promise<AxiosResponse<NewAssigned>> {
    const response = await this.client.patch(
      '/secure-contacts/assigned/{secureContactId}',
      {
        params: {
          header: {
            'x-trade-company-id': companyId,
          },
          path: {
            secureContactId,
          },
        },
        body: {
          contactId: contactId,
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(newAssignedSchema, response.data),
    };
  }
}

export const secureContactsTradeBffApi = new SecureContactsTradeBffApi();
