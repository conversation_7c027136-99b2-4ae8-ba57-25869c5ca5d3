import { safeNativeEnum } from 'src/data/schemas/helpers/safeNativeEnum';
import z from 'zod';

export const assignedSchema = z.object({
  id: z.string(),
  secureNumber: z.string(),
  destinationNumber: z.string(),
});

export enum AssignedType {
  MOBILE = 'MOBILE',
  LANDLINE = 'LANDLINE',
  DIRECTORY = 'DIRECTORY',
}

export enum ProviderSyncStatus {
  SCHEDULED = 'SCHEDULED',
  PENDING = 'PENDING',
  SYNCED = 'SYNCED',
}

export const divertSchema = z.object({
  locked: z.boolean().optional(),
  secureNumber: z.string().optional(),
  displaySecureNumber: z.string().optional(),
  destinationNumber: z.string().optional(),
  ntsNumber: z.string().optional(),
  syncStatus: safeNativeEnum(ProviderSyncStatus).optional(),
});

export const newAssignedSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  number: z.string().optional(),
  type: safeNativeEnum(AssignedType).optional(),
  areaCode: z.string().optional(),
  contactId: z.string().optional(),
  divert: divertSchema.optional(),
  tag: z.string().optional(),
});

export type Assigned = z.infer<typeof assignedSchema>;
export type NewAssigned = z.infer<typeof newAssignedSchema>;
