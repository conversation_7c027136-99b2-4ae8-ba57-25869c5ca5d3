import z from 'zod';

export const formattedOptionSchema = z.object({
  id: z.string(),
  name: z.string(),
  number: z.string(),
});

export const secureContactsOptionsResponseSchema = z.object({
  mobileOptions: z.array(formattedOptionSchema),
});

export type SecureContactsOptionsResponse = z.infer<
  typeof secureContactsOptionsResponseSchema
>;
export type FormattedOption = z.infer<typeof formattedOptionSchema>;
