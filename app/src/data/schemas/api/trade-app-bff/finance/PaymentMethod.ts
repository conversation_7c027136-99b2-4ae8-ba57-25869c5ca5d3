import { z } from 'zod';

export const addressSchema = z.object({
  line1: z.string().optional(),
  line2: z.string().optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  state: z.string().optional(),
  postal_code: z.string().optional(),
});

export const stateEnum = {
  active: 'active',
  closed: 'closed',
  scrubbed: 'scrubbed',
} as const;

export const paymentMethodSchema = z.object({
  id: z.string(),
  updated_by_id: z.string(),
  updated_time: z.string(),
  created_by_id: z.string(),
  created_time: z.string(),
  account_id: z.string(),
  billing_details: z.object({
    name: z.string().optional(),
    address: addressSchema.optional(),
    email: z.string().optional(),
    phone: z.string().optional(),
  }),
  bacs_debit: z.object({
    account_number: z.string(),
    bank_code: z.string(),
    mandate: z
      .object({
        id: z.string().optional(),
        reason: z.string().optional(),
        state: z.enum(['active', 'closed', 'scrubbed']).optional(),
      })
      .optional(),
  }),
  state: z.enum(['active', 'closed', 'scrubbed']).optional(),
  existing_mandate: z.boolean().optional(),
  total_number_of_processed_payments: z.number().optional(),
  total_number_of_error_payments: z.number().optional(),
  last_transaction_time: z.string().datetime({ offset: true }).optional(),
  last_transaction_status: z.string().optional(),
});

export const zuoraStatusSchema = z.object({
  defaultPaymentMethodState: z.enum(['active', 'closed', 'scrubbed']),
});

export const paymentMethodParamsSchema = z.object({
  paymentMethodId: z.string(),
});

export type PaymentMethodParams = z.infer<typeof paymentMethodParamsSchema>;
export type ZuoraStatus = z.infer<typeof zuoraStatusSchema>;
export type PaymentMethod = z.infer<typeof paymentMethodSchema>;
