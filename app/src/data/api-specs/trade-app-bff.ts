/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  '/accounts': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Fetch available trade accounts
     * @description Returns all available accounts as pairs of a company and a trade to switched between
     */
    get: operations['getAccounts'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appointment/types': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get available appointment types
     * @description Provides list of all available appointment types
     */
    get: operations['getAppointmentTypes'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appointment/cancellation-reasons': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get appointments cancellation reasons
     * @description Get trade appointments cancellation reasons list
     */
    get: operations['getTradeCancellationReasons'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appointments': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Offer a new appointment
     * @description Trade offers a new appointment for a job with time options.
     */
    post: operations['postAppointment'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appointments/{appointmentId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get an appointment details
     * @description Trade gets an appointment details for a job.
     */
    get: operations['getAppointment'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    /**
     * Update existing appointment
     * @description Trade wants to change the existing appointment for a job with time options.
     */
    patch: operations['patchAppointment'];
    trace?: never;
  };
  '/appointments/{appointmentId}/cancel': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Cancel existing appointment
     * @description Trade wants to cancel appointment for a job.
     */
    post: operations['postAppointmentCancel'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appointments/{appointmentId}/reschedule': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Reschedule existing appointment
     * @description Trade wants to reschedule appointment for a job.
     */
    post: operations['postAppointmentReschedule'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/quotes/{quoteId}/share': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Shares the quote to consumer, renders update to trader
     * @description Shares a Quote with a consumer, decorating it with Opportunity data before sending a message.
     */
    post: operations['postQuoteShare'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/quotes/{quoteId}/draft': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Shares a draft quote/invoice
     * @description Initial quote/invoice quote created for trader rendering purposes only
     */
    post: operations['postQuoteDraft'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/quotes/{quoteId}/token': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Create a quoting time limited token
     * @description Creates a time limited token for allowing consumers without a CAT account to view their quote
     */
    post: operations['postQuoteToken'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/quotes/{quoteId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    /**
     * Delete quote. This is a hard delete.
     * @description Delete the quote with the given id, cancel any associated payments and send a chat message notifying consumer and trade that the quote is deleted.
     */
    delete: operations['deleteQuote'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/quotes': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Creates a quote
     * @description Creates a quote
     */
    post: operations['createQuote'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/quotes/{quoteId}/details': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Gets a quote's details
     * @description Gets a quote's details
     */
    get: operations['GetsQuoteDetails'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/quotes/job/{jobId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Gets all quotes associated with a job ID
     * @description Gets quotes associated with a job ID
     */
    get: operations['GetQuotesByJobId'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/quotes/opportunity/{opportunityId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Gets all quotes associated with an opportunity ID
     * @description Gets quotes associated with an opportunity ID
     */
    get: operations['getQuotesByOpportunityId'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/reviews/{reviewId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get a review
     * @description Fetches a single review by id
     */
    get: operations['getReview'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/reviews/metrics': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get review stat summary
     * @description Fetches a review stat summary
     */
    get: operations['getReviewsMetrics'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/reviews/summary/{companyId}/{summaryType}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get reviews summary
     * @description Retrieves the summary of reviews for a specific company and summary type
     */
    get: {
      parameters: {
        query?: never;
        header: {
          'x-trade-company-id': number;
        };
        path: {
          companyId: number;
          summaryType: 'trade' | 'consumer';
        };
        cookie?: never;
      };
      requestBody?: never;
      responses: {
        /** @description Default Response */
        200: {
          headers: {
            [name: string]: unknown;
          };
          content: {
            'application/json': {
              companyId: number;
              summary:
                | {
                    summary: string;
                    strengths: string[];
                    improvements: string[];
                  }
                | {
                    summary: string;
                  }
                | null;
              /** Format: date-time */
              updatedAt: string;
            };
          };
        };
      };
    };
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/reviews': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Gets reviews list
     * @description Fetches a list of reviews for a companyId
     */
    get: operations['getReviewsList'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/reviews/{reviewId}/reply': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Post review reply
     * @description Create a review reply
     */
    post: operations['postReviewReply'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/reviews/{reviewId}/report': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Post review report
     * @description Create a review report
     */
    post: operations['postReviewReport'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/review-reply/{replyId}/unpublish': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Post review reply unpublish
     * @description Unpublish review reply
     */
    post: operations['postReviewReplyUnpublish'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/review-request/manual': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Post manual review request
     * @description record that manual review request was made
     */
    post: operations['postReviewRequestManual'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/manual-review-requests': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Gets manual review requests list
     * @description Fetches a list of review requests for companyId
     */
    get: operations['getManualReviewRequestList'];
    put?: never;
    /**
     * Post manual review request
     * @description record that manual review request was made
     */
    post: operations['postManualReviewRequestV2'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/manual-review-requests/{id}/reminder': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Post manual review request
     * @description Post a manual review request reminder
     */
    post: operations['postManualReviewRequestReminder'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/reviews/job-requests': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get reviewable jobs with review request status
     * @description Get paginated reviewable jobs with review request status
     */
    get: operations['getJobReviewRequestList'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/archived-jobs/{jobId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get archived job
     * @description Get archived job by id
     */
    get: operations['getArchivedJob'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/archived-jobs': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get archived jobs
     * @description Get paginated consumer archived jobs
     */
    get: operations['getArchivedJobs'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/consumers/{consumerId}/report': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Report consumer
     * @description For trades reporting/blocking a consumer by job id and consumer id
     */
    post: operations['postReportConsumer'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/auth/chat': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Fetch chat configuration
     * @description Returns an Stream API key and access token for the current user.
     */
    post: operations['postChatToken'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/auth/createCustomFirebaseToken': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Return firebase token from an authenticated request.
     * @description Handles an authenticated request, uses the claims in the auth token to create a token to use with firebase.
     */
    get: operations['getFirebaseToken'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/jobs/{jobId}/appointments': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get trade job appointments
     * @description Get paginated appointments for a trade job
     */
    get: operations['getJobAppointments'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/jobs/{jobId}/review/requests': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Job review requests
     * @description Returns a list of review requests for a job, sent by the trade.
     */
    get: operations['getJobReviewRequests'];
    put?: never;
    /**
     * Request a review for a job
     * @description Publishes a pub/sub message for comms to send an email to the consumer and sends a smart message to the opportunity channel
     */
    post: operations['postJobReviewRequest'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/jobs/{jobId}/property-facts': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get job property facts
     * @description Get property facts about job, including lat/lng, by job id
     */
    get: operations['getJobPropertyFacts'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/jobs/{jobId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get job
     * @description Get by id
     */
    get: operations['getJob'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/jobs': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get jobs
     * @description Get paginated consumer jobs
     */
    get: operations['getJobs'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/jobs/count-unread': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Unread jobs count
     * @description Get count of jobs not yet viewed by trade
     */
    get: operations['getUnreadJobsCountJobs'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/jobs/{jobId}/accept': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Accept opportunity
     * @description Trade raise his interest in the job. Changes job status to REQUEST_ACCEPTED.
     */
    post: operations['postAcceptJob'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/jobs/{jobId}/cancel': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Cancel opportunity
     * @description Trade marks job as cancelled. Changes opportunity status to CANCELLED.
     */
    post: operations['postCancelJob'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/jobs/{jobId}/complete': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Complete opportunity
     * @description Trade marks job as complete. Changes opportunity status to COMPLETED.
     */
    post: operations['postCompleteJob'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/jobs/{jobId}/book': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Book opportunity
     * @description Trade marks job as booked. Doesn't change status, but instead adds a flag under TradeMarkedBooked
     */
    post: operations['postJobBook'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/jobs/{jobId}/reject': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Reject opportunity
     * @description Trade is not interested in the job. Changes job status to REQUEST_REJECTED and locks the Stream channel.
     */
    post: operations['postRejectJob'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/jobs/{jobId}/request-address': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Request address
     * @description Requests the consumers address.
     */
    post: operations['postRequestAddress'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/jobs/{jobId}/mark-as-read': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Mark opportunity as read
     * @description Trade views job in trade app or website.
     */
    post: operations['postViewedJob'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/jobs/{jobId}/note': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    /**
     * Update job note
     * @description Trade can add or update a note in a job.
     */
    put: operations['putJobNote'];
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/job/cancel-reasons': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get cancel reasons
     * @description Get list of cancel reasons
     */
    get: operations['getCancelReasons'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/job/reject-reasons': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get reject reasons
     * @description Get list of reject reasons
     */
    get: operations['getRejectReasons'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/activities': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Gets latest payment activities from core payment
     * @description Gets the authenticated user's activities from core-payments
     */
    get: operations['getActivities'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/report/activities-statement': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get payment activities csv report
     * @description Get payment activities csv report for the given time period
     */
    get: operations['getActivitiesStatementReport'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/balance': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Gets the balance of the company
     * @description Gets the balance and outstanding balance of the company
     */
    get: operations['getBalance'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/balance-accounts': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Gets the balance account id of the company and merchant
     * @description Gets the balance account id of the company and merchant
     */
    get: operations['getBalanceAccountIds'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/external-tax-information': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Fetch trade external tax information
     * @description Retrieves external tax information from the KYC Service via the Payment API
     */
    get: operations['getExternalTaxInformation'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/onboarding-information': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Returns a breakdown of current onboarding progress
     * @description Receives onboarding information and percentage alongside the capabilities
     */
    get: operations['getOnboardingInformation'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/pay-by-phone/payment-methods': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Gets available payment methods for the company
     * @description Retrieves list of available payment methods for the company
     */
    get: operations['getPaymentMethods'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/payment-request/{paymentLinkId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get Payment Request
     * @description Receives a payment request for a payment link id
     */
    get: operations['getPaymentRequest'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/payment-requests/opportunity/{opportunityId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * payment requests for the specified opportunity
     * @description returns an array of payment requests for the given opportunityId
     */
    get: operations['getPaymentRequestsByOpportunity'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/payment-requests/quote/{quoteId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * payment requests for the specified quote
     * @description returns an array of payment requests that are associated with the given quoteId
     */
    get: operations['getPaymentRequestsByQuote'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/payment-requests/quote/{quoteId}/metrics': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * metrics for a quote and associated payments
     * @description returns the shared metrics between a quote and associated payments
     */
    get: operations['getPaymentRequestsQuoteMetrics'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/split-payment/{penceAmount}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Gets the payment service charge and total for the provided amount in pennies
     * @description Gets the payment service charge and total the consumer will pay (penceAmount + service charge) for the provided amount in pennies
     */
    get: operations['getSplitPayment'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/tax-information': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Fetch trade tax information
     * @description Retrieves tax information from ContentApi
     */
    get: operations['getTaxInformation'];
    put?: never;
    /**
     * Post Tax Information
     * @description Any updates to tax information, broadcasts into GCP and consumed by Agent tooling.
     */
    post: operations['postTaxInformation'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/payment-request/{paymentLinkId}/cancel': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    /**
     * Create Payment Request
     * @description Cancel payment request
     */
    patch: operations['cancelPaymentRequest'];
    trace?: never;
  };
  '/payments/onboard': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Fetch profile data and onboard
     * @description Retrieves data from ContentApi which is used to seed Adyen, then returns onboard information
     */
    post: operations['postOnboard'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/pay-by-phone/payment-details': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Create Payment Details for pay by phone
     * @description Creates a new payment details for pay by phone
     */
    post: operations['postPayByPhonePaymentDetails'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/pay-by-phone/payment-request': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Create Payment Request for pay by phone
     * @description Creates a new payment request for pay by phone
     */
    post: operations['postCreatePayByPhonePaymentRequest'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/payment-request': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Create Payment Request
     * @description Creates a new payment request and sends a stream message with the payment link once successfully created
     */
    post: operations['postCreatePaymentRequest'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/payment-request/off-platform-job': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Create Payment Request for an off platform job
     * @description Creates a new payment request and return a link to share with the consumer
     */
    post: operations['postCreatePaymentRequestOffPlatformJob'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/payments/session': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Post Session
     * @description Get a session for running tap to pay capabilities
     */
    post: operations['postTerminalSession'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/referral': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get a referral factory code
     * @description Fetches a referral factory code for a trade
     */
    get: operations['getReferralCode'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/zuora/account': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get Zuora account information
     * @description Fetches Zuora account information for a company
     */
    get: operations['getZuoraAccount'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/zuora/payment-methods/{paymentMethodId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get payment method information from Zuora
     * @description Fetches payment method information for an id from Zuora
     */
    get: operations['getZuoraPaymentMethod'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/zuora/status': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Gets Zuora status
     * @description Fetches Zuora status, including the state of the default payment method.
     */
    get: operations['getZuoraStatus'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/zuora/account/{accountId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    /**
     * update Zuora account information with new defaultPaymentMethodId
     * @description Updates Zuora account information for an account
     */
    patch: operations['patchZuoraAccount'];
    trace?: never;
  };
  '/zuora/dd-update-rsa-signature': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Post RSA signature for Zuora
     * @description Creates DD RSA signature for Zuora integration
     */
    post: operations['postZuoraRsaSignature'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Add team persons
     * @description Add team persons
     */
    post: operations['addTeamPersons'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team/{personId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get team person
     * @description Get team person
     */
    get: operations['getTeamPerson'];
    put?: never;
    post?: never;
    /**
     * Delete team person
     * @description Delete team person
     */
    delete: operations['deleteTeamPerson'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team/persons': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get team persons
     * @description Get team persons
     */
    get: operations['getTeamPersons'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team/invite/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get team invite
     * @description Get team invite
     */
    get: operations['getTeamInvite'];
    /**
     * Update team invite
     * @description Trade can update a team invite
     */
    put: operations['putTeamInvite'];
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team/invites': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get team invites
     * @description Get team invites
     */
    get: operations['getTeamInvites'];
    put?: never;
    /**
     * Invite subcontractor
     * @description Create a subcontractor invite link
     */
    post: operations['inviteSubcontractor'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team/persons/vetting-details': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get team person vetting details
     * @description Based on the company ID & email, get the current person vetting details
     */
    get: operations['getTeamPersonsVettingDetails'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team/{personId}/persons': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    /**
     * Update team person
     * @description Trade can update a team person
     */
    put: operations['putTeamPerson'];
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team/{personId}/consent-email': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Create consent email
     * @description Publish an event to generate a consent email in Braze
     */
    post: operations['addConsentEmailEvent'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team/{personId}/vetting': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    /**
     * Update team person's vetting consent
     * @description Trade can update their vetting consent
     */
    put: operations['putTeamPersonVetting'];
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team/counters': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get team counters
     * @description Get team counters
     */
    get: operations['getTeamCounters'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team/pending-invites': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get team pending invites
     * @description Get team pending invites
     */
    get: operations['getTeamPendingInvites'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team/vetting-consent-token-decode': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Decode vetting consent token
     * @description Decode vetting consent token
     */
    get: operations['getVettingConsentTokenDecode'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team/subcontractor/{subcontractorId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    /**
     * Delete subcontractor
     * @description Delete subcontractor
     */
    delete: operations['deleteSubcontractor'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team/vetting-details': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get team vetting status
     * @description Get team (company) vetting status via companyId header
     */
    get: operations['getTeamVettingStatus'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/team/is-full-member-or-national-account': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get is full member or national account
     * @description Get is full member or national account
     */
    get: operations['getIsFullMemberOrNationalAccount'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/person/{personId}/accreditations': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get person accreditations
     * @description Get all accreditations for a person
     */
    get: operations['getPersonAccreditations'];
    put?: never;
    /**
     * Post accreditation
     * @description Create new accreditation or overwrite existing
     */
    post: operations['postAccreditation'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/person/{personId}/accreditations/{accreditationId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get person accreditation
     * @description Get accreditation details for a person.
     */
    get: operations['getPersonAccreditation'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/person/{personId}/accreditations/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    /**
     * Delete accreditation
     * @description Soft delete accreditation by accreditation
     */
    delete: operations['deletePersonAccreditation'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/person/{personId}/required-accreditations': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get person accreditations
     * @description Get all accreditations for a person
     */
    get: operations['getPersonRequiredAccreditations'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/self-service/company-name': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Update company name
     * @description Update the company name
     */
    post: operations['updateCompanyName'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/self-service/member-email': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Update member email
     * @description Update the member email address
     */
    post: operations['updateMmeberEmail'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/self-service/member-phone': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Update member phone
     * @description Update the member phone number
     */
    post: operations['updateMmeberPhone'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/self-service/member-trading-address': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Update member trading address
     * @description Update the member trading address, which is the primary postal address associated with their account
     */
    post: operations['updateMemberTradingAddress'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/self-service/member-admin-address': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Update member admin address
     * @description Update the member admin address, which is the secondary postal address associated with their account
     */
    post: operations['updateMemberAdminAddress'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/self-service/update-person': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Update person details
     * @description Update the details of a person in the company, which is a contact associate with their account
     */
    post: operations['updatePerson'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/self-service/delete-person': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Delete Person
     * @description Allows a member to make a request to delete a person from their account (contact)
     */
    post: operations['updateMemberDeletePerson'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/service-catalog/service/{serviceId}/version/{versionId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Retrieves a service version for a company
     * @description Retrieves a service version for all current services for a company
     */
    get: operations['getServiceVersion'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/service-catalog/services': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Retrieves a list of services for a company
     * @description Retrieves a list of services for a company, including only the current service version
     */
    get: operations['getServices'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/service-catalog/service': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Creates a new service for a company
     * @description Creates a new service for a company with the provided details
     */
    post: operations['postService'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/service-catalog/service/{serviceId}/version': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Creates a new service version for a company
     * @description Creates a new service version for a company with the provided details, or updates the current version if editable
     */
    post: operations['postServiceVersion'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/service-catalog/service/{serviceId}/version/{versionId}/status': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    /**
     * Update status on service version
     * @description Updates the status of a service version.
     */
    patch: operations['patchServiceVersionStatus'];
    trace?: never;
  };
  '/service-catalog/essential-trade/service': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Creates a new service for an essential trade company
     * @description Creates a new service for an essential trade
     */
    post: operations['postEssentialTradeService'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/service-catalog/essential-trade/service/{serviceId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    /**
     * Deletes a service for an essential trade company
     * @description Archives a service for an essential trade
     */
    delete: operations['deleteEssentialTradeService'];
    options?: never;
    head?: never;
    /**
     * Updates a service for an essential trade company
     * @description Updates a service for an essential trade
     */
    patch: operations['patchEssentialTradeService'];
    trace?: never;
  };
  '/member-info': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Fetch member info
     * @description Returns the member information for the authenticated user.
     */
    get: operations['getMemberInfo'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/member-details': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Fetch member details
     * @description Returns detailed member information for the authenticated user.
     */
    get: operations['getMemberDetails'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/revetting/status': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get re-vetting status
     * @description Get re-vetting status
     */
    get: operations['getRevettingStatus'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/revetting/url': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get re-vetting url
     * @description Get re-vetting url
     */
    get: operations['getRevettingUrl'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/ad-manager/advertisers/{companyId}/campaigns': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Create Sponsored Listings campaigns
     * @description Creates new Sponsored Listings campaigns for a company and returns the campaign IDs
     */
    post: operations['createCampaigns'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/campaigns/stats': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get campaign statistics
     * @description Returns statistics summary for all campaigns
     */
    get: operations['getCampaignStats'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/ad-manager/experiences': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get experiences
     * @description Returns available experiences for ad manager including experience IDs, names, currency types, bid ranges, and bid strategies
     */
    get: operations['getExperiences'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/project': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Post project
     * @description Create a new featured project
     */
    post: operations['postProject'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/project/{projectId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get project
     * @description Retrieve a project by ID
     */
    get: operations['getProject'];
    put?: never;
    post?: never;
    /**
     * Delete project
     * @description Delete a project by ID
     */
    delete: operations['deleteProject'];
    options?: never;
    head?: never;
    /**
     * Update project
     * @description Partially update a project by ID
     */
    patch: operations['updateProject'];
    trace?: never;
  };
  '/projects': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * List company projects
     * @description Retrieve a list of projects for a specific company
     */
    get: operations['listCompanyProjects'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/projects/completed-jobs': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * List of completed jobs with a flag indicating if they are assigned to a project
     * @description Gets a list of jobs that are completed and checks if they are assigned to a project. The response includes job details and a flag indicating assignment status.
     */
    get: operations['projectCompletedJobs'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/trade-insights': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Fetch company trade insights
     * @description Returns trade insights data
     */
    get: operations['gettradeinsights'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/secure-contacts/options': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get Secure Contacts options
     * @description Fetches secure contact options for a company
     */
    get: operations['getSecureContactsOptions'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/secure-contacts/assigned': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get assigned Secure Contacts
     * @description Fetches assigned secure contact for a company
     */
    get: operations['getAssignedSecureContacts'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/secure-contacts/assigned/{secureContactId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    /**
     * patch assigned Secure Contact
     * @description assigns a new contact to a secure contact
     */
    patch: operations['patchAssignedSecureContact'];
    trace?: never;
  };
}
export type webhooks = Record<string, never>;
export interface components {
  schemas: never;
  responses: never;
  parameters: never;
  requestBodies: never;
  headers: never;
  pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
  getAccounts: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            companyId: number;
            traderId?: number;
            companyName?: null | string;
            companyLogo?: null | string;
            vettingStatus: number;
            salesforceVettingStatus: string;
          }[];
        };
      };
      /** @description Default Response */
      401: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  getAppointmentTypes: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            title: string;
            customTitle?: boolean;
            category: 'JOB_START' | 'OTHER';
          }[];
        };
      };
    };
  };
  getTradeCancellationReasons: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** Format: uuid */
            id: string;
            text: string;
            customText?: boolean;
          }[];
        };
      };
    };
  };
  postAppointment: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          /**
           * Format: uuid
           * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
           */
          jobId: string;
          /**
           * Format: uuid
           * @description Identifier in the UUID format
           */
          consumerId: string;
          type: {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            customTitle?: string;
          };
          alternatives: {
            /** Format: date-time */
            start: string;
            /** Format: date-time */
            end?: string;
          }[];
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            jobId: string;
            /**
             * Format: uuid
             * @description Identifier in the UUID format
             */
            consumerId: string;
            companyId: number;
            type: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              title: string;
              category: 'JOB_START' | 'OTHER';
            };
            cancellationReason?: {
              /** Format: uuid */
              id: string;
              text: string;
            };
            alternatives: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /** Format: date-time */
              start: string;
              /** Format: date-time */
              end?: string;
              status: 'CREATED' | 'ACCEPTED' | 'REJECTED' | 'CANCELLED';
            }[];
            status: 'CREATED' | 'ACCEPTED' | 'REJECTED' | 'CANCELLED';
          };
        };
      };
    };
  };
  getAppointment: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        appointmentId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            jobId: string;
            /**
             * Format: uuid
             * @description Identifier in the UUID format
             */
            consumerId: string;
            companyId: number;
            type: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              title: string;
              category: 'JOB_START' | 'OTHER';
            };
            cancellationReason?: {
              /** Format: uuid */
              id: string;
              text: string;
            };
            alternatives: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /** Format: date-time */
              start: string;
              /** Format: date-time */
              end?: string;
              status: 'CREATED' | 'ACCEPTED' | 'REJECTED' | 'CANCELLED';
            }[];
            status: 'CREATED' | 'ACCEPTED' | 'REJECTED' | 'CANCELLED';
          };
        };
      };
    };
  };
  patchAppointment: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        /** @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com */
        appointmentId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          type?: {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            customTitle?: string;
          };
          alternatives?: {
            /** Format: date-time */
            start: string;
            /** Format: date-time */
            end?: string;
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id?: string;
          }[];
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            jobId: string;
            /**
             * Format: uuid
             * @description Identifier in the UUID format
             */
            consumerId: string;
            companyId: number;
            type: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              title: string;
              category: 'JOB_START' | 'OTHER';
            };
            cancellationReason?: {
              /** Format: uuid */
              id: string;
              text: string;
            };
            alternatives: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /** Format: date-time */
              start: string;
              /** Format: date-time */
              end?: string;
              status: 'CREATED' | 'ACCEPTED' | 'REJECTED' | 'CANCELLED';
            }[];
            status: 'CREATED' | 'ACCEPTED' | 'REJECTED' | 'CANCELLED';
          };
        };
      };
    };
  };
  postAppointmentCancel: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        /** @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com */
        appointmentId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          reason?: {
            /** Format: uuid */
            id: string;
            customText?: string;
          };
        } | null;
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            jobId: string;
            /**
             * Format: uuid
             * @description Identifier in the UUID format
             */
            consumerId: string;
            companyId: number;
            type: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              title: string;
              category: 'JOB_START' | 'OTHER';
            };
            cancellationReason?: {
              /** Format: uuid */
              id: string;
              text: string;
            };
            alternatives: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /** Format: date-time */
              start: string;
              /** Format: date-time */
              end?: string;
              status: 'CREATED' | 'ACCEPTED' | 'REJECTED' | 'CANCELLED';
            }[];
            status: 'CREATED' | 'ACCEPTED' | 'REJECTED' | 'CANCELLED';
          };
        };
      };
    };
  };
  postAppointmentReschedule: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        /** @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com */
        appointmentId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          /**
           * Format: uuid
           * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
           */
          jobId: string;
          /**
           * Format: uuid
           * @description Identifier in the UUID format
           */
          consumerId: string;
          type: {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            customTitle?: string;
          };
          alternatives: {
            /** Format: date-time */
            start: string;
            /** Format: date-time */
            end?: string;
          }[];
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            jobId: string;
            /**
             * Format: uuid
             * @description Identifier in the UUID format
             */
            consumerId: string;
            companyId: number;
            type: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              title: string;
              category: 'JOB_START' | 'OTHER';
            };
            cancellationReason?: {
              /** Format: uuid */
              id: string;
              text: string;
            };
            alternatives: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /** Format: date-time */
              start: string;
              /** Format: date-time */
              end?: string;
              status: 'CREATED' | 'ACCEPTED' | 'REJECTED' | 'CANCELLED';
            }[];
            status: 'CREATED' | 'ACCEPTED' | 'REJECTED' | 'CANCELLED';
          };
        };
      };
    };
  };
  postQuoteShare: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        quoteId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': Record<string, never>;
        };
      };
    };
  };
  postQuoteDraft: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        quoteId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': Record<string, never>;
        };
      };
    };
  };
  postQuoteToken: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        /** @description The quote id */
        quoteId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            token: string;
          };
        };
      };
    };
  };
  deleteQuote: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        /** @description ID of the quote */
        quoteId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            quoteId: string;
            deletedAt: string;
          };
        };
      };
    };
  };
  createQuote: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          /** @default 0 */
          discountAmount: number;
          consumerDetails: {
            address: string;
            addressLine2?: string | null;
            city: string;
            emailAddress?: string | null;
            name: string;
            postcode: string;
            phoneNumber?: string | null;
          };
          jobId?: string | null;
          jobIdV2?: string | null;
          opportunityId?: string | null;
          consumerId?: string | null;
          isMarketplaceJob: boolean;
          lineItems: {
            description?: string | null;
            hyperlink: string;
            /** @default 0 */
            discountAmount: number;
            /** @default 0 */
            numberOfUnits: number;
            /** @default 0 */
            pricePerUnit: number;
            /** @default 0 */
            subtotalAmount: number;
            title: string;
            /** @default 0 */
            totalAmount: number;
            type: 'TIME' | 'MATERIAL' | 'MISCELLANEOUS';
            /** @default 0 */
            vatAmount: number;
            /** @default 0 */
            vatRate: number;
          }[];
          referenceNumber: string;
          /** @default 0 */
          subtotalAmount: number;
          terms?: string | null;
          title: string;
          /** @default 0 */
          totalAmount: number;
          /** @default 0 */
          vatAmount: number;
          vatRegistrationNumber?: string | null;
          company: {
            name: string;
            /** @default 0 */
            id: number;
          };
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            quoteId: string;
          };
        };
      };
    };
  };
  GetsQuoteDetails: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        quoteId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            companyId?: number;
            title: string;
            /** Format: date-time */
            createdAt: string;
            /** @default 0 */
            totalAmount: number;
            type: 'INVOICE' | 'QUOTE';
            status:
              | 'DRAFT'
              | 'SHARED'
              | 'ACCEPTED'
              | 'REJECTED'
              | 'PAID'
              | 'OVERDUE';
            reference: string;
            pdfFileUrl: string;
            opportunityId?: string;
            /** Format: date-time */
            acceptedAt?: string;
            /** Format: date-time */
            rejectedAt?: string;
            jobId: string;
            jobIdV2?: string;
            /** @default 0 */
            discountAmount: number;
            consumerDetails: {
              address: string;
              addressLine2?: string | null;
              city: string;
              emailAddress?: string | null;
              name: string;
              postcode: string;
              phoneNumber?: string | null;
            };
            consumerId?: string | null;
            isMarketplaceJob: boolean;
            lineItems: {
              description?: string | null;
              hyperlink: string;
              /** @default 0 */
              discountAmount: number;
              /** @default 0 */
              numberOfUnits: number;
              /** @default 0 */
              pricePerUnit: number;
              /** @default 0 */
              subtotalAmount: number;
              title: string;
              /** @default 0 */
              totalAmount: number;
              type: 'TIME' | 'MATERIAL' | 'MISCELLANEOUS';
              /** @default 0 */
              vatAmount: number;
              /** @default 0 */
              vatRate: number;
              id: string;
            }[];
            /** @default 0 */
            subtotalAmount: number;
            terms?: string | null;
            /** @default 0 */
            vatAmount: number;
            vatRegistrationNumber?: string | null;
            invoice?: {
              createdAt?: string | null;
              dueAt?: string | null;
              paidAt?: string | null;
            } | null;
            /** Format: date-time */
            sharedAt: string;
            /** Format: date-time */
            updatedAt: string;
            pdfReference?: string | null;
          };
        };
      };
    };
  };
  GetQuotesByJobId: {
    parameters: {
      query?: {
        limit?: number;
        lastVisible?: string;
      };
      header: {
        'x-trade-company-id': number;
      };
      path: {
        jobId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            data: {
              id: string;
              companyId?: number;
              title: string;
              /** Format: date-time */
              createdAt: string;
              totalAmount: number;
              type: 'INVOICE' | 'QUOTE';
              status:
                | 'DRAFT'
                | 'SHARED'
                | 'ACCEPTED'
                | 'REJECTED'
                | 'PAID'
                | 'OVERDUE';
              reference: string;
              pdfFileUrl: string;
              opportunityId?: string;
              /** Format: date-time */
              acceptedAt?: string;
              /** Format: date-time */
              rejectedAt?: string;
              jobId: string;
              jobIdV2?: string;
            }[];
            lastVisible: string | null;
          };
        };
      };
    };
  };
  getQuotesByOpportunityId: {
    parameters: {
      query?: {
        limit?: number;
        lastVisible?: string;
      };
      header: {
        'x-trade-company-id': number;
      };
      path: {
        opportunityId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            data: {
              id: string;
              companyId?: number;
              title: string;
              /** Format: date-time */
              createdAt: string;
              totalAmount: number;
              type: 'INVOICE' | 'QUOTE';
              status:
                | 'DRAFT'
                | 'SHARED'
                | 'ACCEPTED'
                | 'REJECTED'
                | 'PAID'
                | 'OVERDUE';
              reference: string;
              pdfFileUrl: string;
              opportunityId?: string;
              /** Format: date-time */
              acceptedAt?: string;
              /** Format: date-time */
              rejectedAt?: string;
              jobId: string;
              jobIdV2?: string;
            }[];
            lastVisible: string | null;
          };
        };
      };
    };
  };
  getReview: {
    parameters: {
      query: {
        /** @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com */
        consumerId: string;
      };
      header: {
        'x-trade-company-id': number;
      };
      path: {
        /** @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com */
        reviewId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            legacyId?: string;
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            consumerId?: string;
            companyId: number;
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            jobId?: string;
            type: 'WORK_NOT_CARRIED' | 'WORK_CARRIED';
            status:
              | 'NOT_PUBLISHED'
              | 'PUBLISHED'
              | 'SCHEDULED'
              | 'DO_NOT_PUBLISH'
              | 'PENDING'
              | 'PARKED'
              | 'ARCHIVED';
            categoryId?: number;
            topLevelCategoryId?: number;
            title: string;
            review: string;
            location?: {
              postcode?: string;
            };
            reply?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reply: string;
              status: 'NOT_PUBLISHED' | 'PUBLISHED' | 'PENDING';
              /** Format: date-time */
              createdAt: string;
              /** Format: date-time */
              updatedAt: string;
            };
            priceAsQuoted?:
              | 'AS_ORIGINALLY_QUOTED'
              | 'MORE_THAN_ORIGINALLY_QUOTED'
              | 'LESS_THAN_ORIGINALLY_QUOTED'
              | 'DID_NOT_GET_A_QUOTE'
              | 'REQUESTED_ADDITIONAL_WORK';
            isComplaint?: boolean;
            notRecommendReason?: string;
            valueOfWork?: number;
            verified?: 'VERIFIED' | 'PENDING' | 'FAILED' | 'NOT_VERIFIED';
            isReported?: boolean;
            rating?: {
              qualityOfWorkmanship?: number;
              tidiness?: number;
              reliabilityAndTimekeeping?: number;
              courtesy?: number;
              communication?: number;
              rating?: number;
            };
            /** Format: date-time */
            createdAt: string;
            /** Format: date-time */
            updatedAt: string;
            /** Format: date-time */
            publishedAt?: string;
            reviewer?: {
              firstName: string;
              lastName: string;
              postcode?: string;
            };
          };
        };
      };
    };
  };
  getReviewsMetrics: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            lifeTime?: {
              totalReviewCount: number;
              publishedCount: number;
              complaintCount: number;
              complaintPercentage: number;
              reviewReplyRate: number;
              connectedJobRate: number;
              verifiedCount: number;
              score?: number;
              courtesy: {
                scoreAverage: number;
                scoreCount: number;
              };
              qualityOfWorkmanship: {
                scoreAverage: number;
                scoreCount: number;
              };
              reliabilityAndTimekeeping: {
                scoreAverage: number;
                scoreCount: number;
              };
              tidiness: {
                scoreAverage: number;
                scoreCount: number;
              };
              communication: {
                scoreAverage: number;
                scoreCount: number;
              };
            };
            year?: {
              totalReviewCount: number;
              publishedCount: number;
              complaintCount: number;
              complaintPercentage: number;
              reviewReplyRate: number;
              connectedJobRate: number;
              verifiedCount: number;
              score?: number;
              courtesy: {
                scoreAverage: number;
                scoreCount: number;
              };
              qualityOfWorkmanship: {
                scoreAverage: number;
                scoreCount: number;
              };
              reliabilityAndTimekeeping: {
                scoreAverage: number;
                scoreCount: number;
              };
              tidiness: {
                scoreAverage: number;
                scoreCount: number;
              };
              communication: {
                scoreAverage: number;
                scoreCount: number;
              };
            };
            month?: {
              totalReviewCount: number;
              publishedCount: number;
              complaintCount: number;
              complaintPercentage: number;
              reviewReplyRate: number;
              connectedJobRate: number;
              verifiedCount: number;
              score?: number;
              courtesy: {
                scoreAverage: number;
                scoreCount: number;
              };
              qualityOfWorkmanship: {
                scoreAverage: number;
                scoreCount: number;
              };
              reliabilityAndTimekeeping: {
                scoreAverage: number;
                scoreCount: number;
              };
              tidiness: {
                scoreAverage: number;
                scoreCount: number;
              };
              communication: {
                scoreAverage: number;
                scoreCount: number;
              };
            };
          };
        };
      };
    };
  };
  getReviewsList: {
    parameters: {
      query?: {
        /** @description Number of the page */
        page?: number;
        /** @description Length of the page */
        size?: number;
        /** @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com */
        consumerId?: string;
        contextCompanyId?: number;
        scoreMax?: number;
        scoreMin?: number;
        dateFrom?: string;
        dateTo?: string;
        orderAsc?: 'score' | 'createdAt';
        orderDesc?: 'score' | 'createdAt';
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            data: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              legacyId?: string;
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              consumerId?: string;
              companyId: number;
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              jobId?: string;
              type: 'WORK_NOT_CARRIED' | 'WORK_CARRIED';
              status:
                | 'NOT_PUBLISHED'
                | 'PUBLISHED'
                | 'SCHEDULED'
                | 'DO_NOT_PUBLISH'
                | 'PENDING'
                | 'PARKED'
                | 'ARCHIVED';
              categoryId?: number;
              topLevelCategoryId?: number;
              title: string;
              review: string;
              location?: {
                postcode?: string;
              } & {
                postcode?: string;
              };
              reply?: {
                /**
                 * Format: uuid
                 * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
                 */
                id: string;
                reply: string;
                status: 'NOT_PUBLISHED' | 'PUBLISHED' | 'PENDING';
                /** Format: date-time */
                createdAt: string;
                /** Format: date-time */
                updatedAt: string;
              };
              priceAsQuoted?:
                | 'AS_ORIGINALLY_QUOTED'
                | 'MORE_THAN_ORIGINALLY_QUOTED'
                | 'LESS_THAN_ORIGINALLY_QUOTED'
                | 'DID_NOT_GET_A_QUOTE'
                | 'REQUESTED_ADDITIONAL_WORK';
              isComplaint?: boolean;
              notRecommendReason?: string;
              verified?: 'VERIFIED' | 'PENDING' | 'FAILED' | 'NOT_VERIFIED';
              isReported?: boolean;
              rating?: {
                qualityOfWorkmanship?: number;
                tidiness?: number;
                reliabilityAndTimekeeping?: number;
                courtesy?: number;
                communication?: number;
                rating?: number;
              };
              /** Format: date-time */
              createdAt: string;
              /** Format: date-time */
              updatedAt: string;
              /** Format: date-time */
              publishedAt?: string;
              reviewer: {
                firstName: string;
                lastName: string;
                postcode?: string;
              } & {
                firstName: string;
                lastName: string;
                postcode: string;
              };
            }[];
            page: number;
            size: number;
            total: number;
          };
        };
      };
    };
  };
  postReviewReply: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        /** @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com */
        reviewId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          reply: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            reply: string;
            status: 'NOT_PUBLISHED' | 'PUBLISHED' | 'PENDING';
            /** Format: date-time */
            createdAt: string;
            /** Format: date-time */
            updatedAt: string;
          };
        };
      };
    };
  };
  postReviewReport: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        /** @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com */
        reviewId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          description: string;
          reason:
            | 'HARASSMENT_OR_ABUSE'
            | 'INAPPROPRIATE'
            | 'UNKNOWN_TO_TRADE'
            | 'SUSPECTED_FAKE'
            | 'OTHER';
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            legacyId?: string;
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            consumerId?: string;
            companyId: number;
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            jobId?: string;
            type: 'WORK_NOT_CARRIED' | 'WORK_CARRIED';
            status:
              | 'NOT_PUBLISHED'
              | 'PUBLISHED'
              | 'SCHEDULED'
              | 'DO_NOT_PUBLISH'
              | 'PENDING'
              | 'PARKED'
              | 'ARCHIVED';
            categoryId?: number;
            topLevelCategoryId?: number;
            title: string;
            review: string;
            location?: {
              postcode?: string;
            };
            reply?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reply: string;
              status: 'NOT_PUBLISHED' | 'PUBLISHED' | 'PENDING';
              /** Format: date-time */
              createdAt: string;
              /** Format: date-time */
              updatedAt: string;
            };
            priceAsQuoted?:
              | 'AS_ORIGINALLY_QUOTED'
              | 'MORE_THAN_ORIGINALLY_QUOTED'
              | 'LESS_THAN_ORIGINALLY_QUOTED'
              | 'DID_NOT_GET_A_QUOTE'
              | 'REQUESTED_ADDITIONAL_WORK';
            isComplaint?: boolean;
            notRecommendReason?: string;
            valueOfWork?: number;
            verified?: 'VERIFIED' | 'PENDING' | 'FAILED' | 'NOT_VERIFIED';
            isReported?: boolean;
            rating?: {
              qualityOfWorkmanship?: number;
              tidiness?: number;
              reliabilityAndTimekeeping?: number;
              courtesy?: number;
              communication?: number;
              rating?: number;
            };
            /** Format: date-time */
            createdAt: string;
            /** Format: date-time */
            updatedAt: string;
            /** Format: date-time */
            publishedAt?: string;
            reviewer?: {
              firstName: string;
              lastName: string;
              postcode?: string;
            };
          };
        };
      };
    };
  };
  postReviewReplyUnpublish: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        /** @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com */
        replyId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            reply: string;
            status: 'NOT_PUBLISHED' | 'PUBLISHED' | 'PENDING';
            /** Format: date-time */
            createdAt: string;
            /** Format: date-time */
            updatedAt: string;
          };
        };
      };
    };
  };
  postReviewRequestManual: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          consumerName: string;
          mobile?: string;
          /** Format: email */
          email?: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            companyId: number;
            consumerName: string;
            mobile?: string;
            /** Format: email */
            email?: string;
            requestCount: number;
            /** Format: date-time */
            createdAt: string;
            /** Format: date-time */
            updatedAt: string;
          };
        };
      };
    };
  };
  getManualReviewRequestList: {
    parameters: {
      query: {
        size: number;
        lastReviewRequestId?: string;
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            data: ({
              id?: string;
              consumerName: string;
              mobile?: string;
              email?: string;
              /** @default false */
              sent: boolean;
              /** Format: date-time */
              dateCreated: string;
              /** @default false */
              canSendReminder: boolean;
            } & {
              canSendReminder: boolean;
            })[];
            lastReviewRequestId?: string;
          };
        };
      };
    };
  };
  postManualReviewRequestV2: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          consumerName: string;
          mobile?: string;
          /** Format: email */
          email?: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id?: string;
            consumerName: string;
            mobile?: string;
            email?: string;
            /** @default false */
            sent: boolean;
            /** Format: date-time */
            dateCreated: string;
            /** @default false */
            canSendReminder: boolean;
          };
        };
      };
    };
  };
  postManualReviewRequestReminder: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id?: string;
            consumerName: string;
            mobile?: string;
            email?: string;
            /** @default false */
            sent: boolean;
            /** Format: date-time */
            dateCreated: string;
            /** @default false */
            canSendReminder: boolean;
          };
        };
      };
    };
  };
  getJobReviewRequestList: {
    parameters: {
      query?: {
        /** @description Page number */
        page?: number;
        /** @description Size of the page */
        size?: number;
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * @description Page number
             * @default 1
             */
            page: number;
            /**
             * @description Size of the page
             * @default 10
             */
            size: number;
            /** @description Total number of items matching the query */
            total: number;
            data: {
              id: string;
              channelId: string;
              consumer: {
                /**
                 * Format: uuid
                 * @description Identifier in the UUID format
                 */
                id: string;
                /**
                 * @description The string must consist only of letters (from any language), combining marks, spaces (that are not control or format characters), hyphens, periods, or single quotes.   Examples of valid values include 'D'Artagnan', 'Иван', 'こん', '안녕', '你好', 'Jean-Luc', 'Dr. John'.   Examples of invalid values include 'John@Doe', 'John123', 'John 😊 Doe'.   For technical documentation refer to: [Unicode Properties](https://www.regular-expressions.info/unicode.html#prop)
                 * @example Joe
                 */
                firstName?: string;
                /**
                 * @description The string must consist only of letters (from any language), combining marks, spaces (that are not control or format characters), hyphens, periods, or single quotes.   Examples of valid values include 'D'Artagnan', 'Иван', 'こん', '안녕', '你好', 'Jean-Luc', 'Dr. John'.   Examples of invalid values include 'John@Doe', 'John123', 'John 😊 Doe'.   For technical documentation refer to: [Unicode Properties](https://www.regular-expressions.info/unicode.html#prop)
                 * @example Joe
                 */
                lastName?: string;
              };
              postcode?: string;
              reviewRequestStatus: {
                canRequestReview: boolean;
                hasSentReviewRequest: boolean;
                hasSentReviewRequestReminder: boolean;
              };
            }[];
          };
        };
      };
    };
  };
  getArchivedJob: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        jobId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            jobId: string;
            companyId: number;
            /** Format: date-time */
            dateCreated: string;
            /** Format: date-time */
            archivedAt: string;
            correlationId?: string;
            title?: string;
            description?: string;
            postcode?: string;
            consumerName?: string;
            consumerDetails?: {
              email?: string;
              phone?: string;
            };
            preferredStart?: {
              option?:
                | 'ASAP'
                | 'WITHIN2_WEEKS'
                | 'WITHIN1_MONTH'
                | 'FLEXIBLE'
                | 'IN_PLANNING'
                | 'URGENT'
                | 'WITHIN1_WEEK'
                | 'WITHIN_FEW_DAYS';
              level?: number;
              label?: string;
            };
            /** Format: date-time */
            dateOffered?: string;
            /** Format: date-time */
            dateDisplayOrder?: string;
            /** Format: date-time */
            dateResponded?: string;
            /** Format: date-time */
            dateUpdated?: string;
            respondedBy?: string;
            declineReason?:
              | 'WRONG_JOB_TYPE'
              | 'TOO_BUSY'
              | 'TOO_FAR'
              | 'UNCLEAR'
              | 'SPAM'
              | 'OTHER';
            declineReasonDescription?: string;
            status?:
              | 'OFFERED'
              | 'ACCEPTED'
              | 'DECLINED'
              | 'TENTATIVELY_ACCEPTED'
              | 'TENTATIVELY_DECLINED';
            acceptedStatus?:
              | 'INTERESTED'
              | 'BOOKED'
              | 'QUOTED'
              | 'COMPLETED'
              | 'LOST';
            /** Format: date-time */
            acceptedStatusDateUpdated?: string;
            /** Format: date-time */
            lastFetchedAt?: string;
            additionalData?: {
              question?: string;
              answer?: string;
            }[];
            closed?: boolean;
            /** Format: date-time */
            dateClosed?: string;
            categoryId?: number;
          };
        };
      };
    };
  };
  getArchivedJobs: {
    parameters: {
      query?: {
        /** @description Number of the page */
        page?: number;
        /** @description Length of the page */
        size?: number;
        filter?: 'ALL' | 'INTERESTED' | 'COMPLETED' | 'ARCHIVE';
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            data: {
              jobId: string;
              companyId: number;
              /** Format: date-time */
              dateCreated: string;
              /** Format: date-time */
              archivedAt: string;
              correlationId?: string;
              title?: string;
              description?: string;
              postcode?: string;
              consumerName?: string;
              consumerDetails?: {
                email?: string;
                phone?: string;
              };
              preferredStart?: {
                option?:
                  | 'ASAP'
                  | 'WITHIN2_WEEKS'
                  | 'WITHIN1_MONTH'
                  | 'FLEXIBLE'
                  | 'IN_PLANNING'
                  | 'URGENT'
                  | 'WITHIN1_WEEK'
                  | 'WITHIN_FEW_DAYS';
                level?: number;
                label?: string;
              };
              /** Format: date-time */
              dateOffered?: string;
              /** Format: date-time */
              dateDisplayOrder?: string;
              /** Format: date-time */
              dateResponded?: string;
              /** Format: date-time */
              dateUpdated?: string;
              respondedBy?: string;
              declineReason?:
                | 'WRONG_JOB_TYPE'
                | 'TOO_BUSY'
                | 'TOO_FAR'
                | 'UNCLEAR'
                | 'SPAM'
                | 'OTHER';
              declineReasonDescription?: string;
              status?:
                | 'OFFERED'
                | 'ACCEPTED'
                | 'DECLINED'
                | 'TENTATIVELY_ACCEPTED'
                | 'TENTATIVELY_DECLINED';
              acceptedStatus?:
                | 'INTERESTED'
                | 'BOOKED'
                | 'QUOTED'
                | 'COMPLETED'
                | 'LOST';
              /** Format: date-time */
              acceptedStatusDateUpdated?: string;
              /** Format: date-time */
              lastFetchedAt?: string;
              additionalData?: {
                question?: string;
                answer?: string;
              }[];
              closed?: boolean;
              /** Format: date-time */
              dateClosed?: string;
              categoryId?: number;
            }[];
            page: number;
            size: number;
            total: number;
          };
        };
      };
    };
  };
  postReportConsumer: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        consumerId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          /** Format: uuid */
          jobId: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': Record<string, never>;
        };
      };
    };
  };
  postChatToken: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Returns an Stream API key and access token for the current user */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            accessToken: string;
            apiKey: string;
          };
        };
      };
    };
  };
  getFirebaseToken: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': string;
        };
      };
      /** @description Default Response */
      401: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  getJobAppointments: {
    parameters: {
      query?: {
        /** @description Page number */
        page?: number;
        /** @description Size of the page */
        size?: number;
        active?: 'all' | 'yes';
      };
      header: {
        'x-trade-company-id': number;
      };
      path: {
        jobId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * @description Page number
             * @default 1
             */
            page: number;
            /**
             * @description Size of the page
             * @default 10
             */
            size: number;
            /** @description Total number of items matching the query */
            total: number;
            data: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              jobId: string;
              status: 'CREATED' | 'ACCEPTED' | 'REJECTED' | 'CANCELLED';
              type: {
                title: string;
                category: 'JOB_START' | 'OTHER';
              };
              alternatives: {
                /**
                 * Format: uuid
                 * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
                 */
                id: string;
                /** Format: date-time */
                start: string;
                /** Format: date-time */
                end?: string;
                status: 'CREATED' | 'ACCEPTED' | 'REJECTED' | 'CANCELLED';
              }[];
            }[];
          };
        };
      };
    };
  };
  getJobReviewRequests: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        jobId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            type: 'REVIEW_REQUESTED' | 'REVIEW_REQUEST_REMINDER';
            /** Format: date-time */
            createdAt: string;
          }[];
        };
      };
    };
  };
  postJobReviewRequest: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        jobId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            success: boolean;
          };
        };
      };
    };
  };
  getJobPropertyFacts: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        /** @description Identifier of the job */
        jobId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            energyRating: string | null;
            primaryHeatingSource: string | null;
            roofType: string | null;
            groundsArea: number | null;
            bedrooms: number | null;
            bathrooms: number | null;
            floorArea: number | null;
            mainsGas: boolean | null;
            garage: boolean | null;
            garden: boolean | null;
            driveway: boolean | null;
            latitude: number | null;
            longitude: number | null;
            periodOfConstruction: string | null;
            dateOfConstruction: number | null;
            propertyType: string | null;
            propertySubtype: string | null;
          };
        };
      };
    };
  };
  getJob: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        jobId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Successful response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            /** Format: date-time */
            createdAt: string;
            description: string;
            /**
             * @description Postcode
             * @example SW1A 1AA
             */
            postcode?: string;
            status:
              | 'REQUEST_ACCEPTED'
              | 'REQUEST_REJECTED'
              | 'SCHEDULED'
              | 'SCHEDULE_CANCELLED'
              | 'DRAFT'
              | 'REQUESTED'
              | 'INTERESTED'
              | 'BOOKED'
              | 'IN_PROGRESS'
              | 'WORK_FINISHED'
              | 'WITHDRAWN'
              | 'COMPLETED'
              | 'CANCELLED'
              | 'V2_CANCELLED';
            preferredStart: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              title: string;
              /** Format: date-time */
              date?: string;
            };
            cancelReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            tradeViewed: boolean;
            tradeMarkedBooked?: boolean;
            fulfilmentType?:
              | 'EMERGENCY_ENQUIRY'
              | 'BOOKING_ENQUIRY'
              | 'ENQUIRY';
            jobRefinementForm: {
              experimentId?: string;
              experimentName?: string;
              variant?: string;
              formId: string;
              questions: {
                questionId: string;
                title: string;
                format: string;
                answers: (
                  | ({
                      key?: string;
                      value: string | null;
                    } & {
                      [key: string]: unknown;
                    })
                  | {
                      key?: string;
                      value: string | null;
                      /** Format: uri */
                      url?: string;
                    }
                )[];
              }[];
            } | null;
            mediaAttachmentIds?: string[];
            booking?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /** Format: date-time */
              createdAt: string;
            };
            details?: {
              questionId: number;
              question: string;
              answerId: number;
              answer: string;
            }[];
            note?: string;
            rejectReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            category: {
              id: number;
              label: string;
            };
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            channelId: string;
            consumer: {
              id: string;
              firstName?: string;
              lastName?: string;
              /** Format: email */
              email?: string;
              phone?: string;
            };
            address: {
              uprn?: string | null;
              city?: string | null;
              postcode: string;
            };
            mediaAttachments?: {
              id: string;
              mimetype: string;
              isPublished: boolean;
              status:
                | 'FAILED_BY_AI'
                | 'FAILED_BY_HUMAN'
                | 'IN_MODERATION'
                | 'PASSED_BY_AI'
                | 'PASSED_BY_HUMAN'
                | 'UNVETTED';
              url: string;
              thumbnailUrl?: string;
            }[];
          };
        };
      };
    };
  };
  getJobs: {
    parameters: {
      query?: {
        tab?:
          | 'NEW'
          | 'ALL'
          | 'INTERESTED'
          | 'COMPLETED'
          | 'ARCHIVE'
          | 'REVIEWABLE'
          | 'QUOTABLE'
          | 'BOOKED';
        /** @description Page number */
        page?: number;
        /** @description Size of the page */
        size?: number;
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * @description Page number
             * @default 1
             */
            page: number;
            /**
             * @description Size of the page
             * @default 10
             */
            size: number;
            /** @description Total number of items matching the query */
            total: number;
            data: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /** Format: date-time */
              createdAt: string;
              description: string;
              /**
               * @description Postcode
               * @example SW1A 1AA
               */
              postcode?: string;
              status:
                | 'REQUEST_ACCEPTED'
                | 'REQUEST_REJECTED'
                | 'SCHEDULED'
                | 'SCHEDULE_CANCELLED'
                | 'DRAFT'
                | 'REQUESTED'
                | 'INTERESTED'
                | 'BOOKED'
                | 'IN_PROGRESS'
                | 'WORK_FINISHED'
                | 'WITHDRAWN'
                | 'COMPLETED'
                | 'CANCELLED'
                | 'V2_CANCELLED';
              cancelReason?: {
                /**
                 * Format: uuid
                 * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
                 */
                id: string;
                reason: string;
              };
              tradeViewed: boolean;
              tradeMarkedBooked?: boolean;
              fulfilmentType?:
                | 'EMERGENCY_ENQUIRY'
                | 'BOOKING_ENQUIRY'
                | 'ENQUIRY';
              jobRefinementForm: {
                experimentId?: string;
                experimentName?: string;
                variant?: string;
                formId: string;
                questions: {
                  questionId: string;
                  title: string;
                  format: string;
                  answers: (
                    | ({
                        key?: string;
                        value: string | null;
                      } & {
                        [key: string]: unknown;
                      })
                    | {
                        key?: string;
                        value: string | null;
                        /** Format: uri */
                        url?: string;
                      }
                  )[];
                }[];
              } | null;
              mediaAttachmentIds?: string[];
              booking?: {
                /**
                 * Format: uuid
                 * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
                 */
                id: string;
                /** Format: date-time */
                createdAt: string;
              };
              details?: {
                questionId: number;
                question: string;
                answerId: number;
                answer: string;
              }[];
              note?: string;
              rejectReason?: {
                /**
                 * Format: uuid
                 * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
                 */
                id: string;
                reason: string;
              };
              category: {
                id: number;
                label: string;
              };
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              channelId: string;
              consumer: {
                id: string;
                firstName?: string;
                lastName?: string;
                /** Format: email */
                email?: string;
                phone?: string;
              };
              mediaAttachments?: {
                id: string;
                mimetype: string;
                isPublished: boolean;
                status:
                  | 'FAILED_BY_AI'
                  | 'FAILED_BY_HUMAN'
                  | 'IN_MODERATION'
                  | 'PASSED_BY_AI'
                  | 'PASSED_BY_HUMAN'
                  | 'UNVETTED';
                url: string;
                thumbnailUrl?: string;
              }[];
            }[];
          };
        };
      };
    };
  };
  getUnreadJobsCountJobs: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            count: number;
          };
        };
      };
    };
  };
  postAcceptJob: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        jobId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Successful response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            /** Format: date-time */
            createdAt: string;
            description: string;
            /**
             * @description Postcode
             * @example SW1A 1AA
             */
            postcode?: string;
            status:
              | 'REQUEST_ACCEPTED'
              | 'REQUEST_REJECTED'
              | 'SCHEDULED'
              | 'SCHEDULE_CANCELLED'
              | 'DRAFT'
              | 'REQUESTED'
              | 'INTERESTED'
              | 'BOOKED'
              | 'IN_PROGRESS'
              | 'WORK_FINISHED'
              | 'WITHDRAWN'
              | 'COMPLETED'
              | 'CANCELLED'
              | 'V2_CANCELLED';
            preferredStart: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              title: string;
              /** Format: date-time */
              date?: string;
            };
            cancelReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            tradeViewed: boolean;
            tradeMarkedBooked?: boolean;
            fulfilmentType?:
              | 'EMERGENCY_ENQUIRY'
              | 'BOOKING_ENQUIRY'
              | 'ENQUIRY';
            jobRefinementForm: {
              experimentId?: string;
              experimentName?: string;
              variant?: string;
              formId: string;
              questions: {
                questionId: string;
                title: string;
                format: string;
                answers: (
                  | ({
                      key?: string;
                      value: string | null;
                    } & {
                      [key: string]: unknown;
                    })
                  | {
                      key?: string;
                      value: string | null;
                      /** Format: uri */
                      url?: string;
                    }
                )[];
              }[];
            } | null;
            mediaAttachmentIds?: string[];
            booking?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /** Format: date-time */
              createdAt: string;
            };
            details?: {
              questionId: number;
              question: string;
              answerId: number;
              answer: string;
            }[];
            note?: string;
            rejectReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            category: {
              id: number;
              label: string;
            };
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            channelId: string;
            consumer: {
              id: string;
              firstName?: string;
              lastName?: string;
              /** Format: email */
              email?: string;
              phone?: string;
            };
            address: {
              uprn?: string | null;
              city?: string | null;
              postcode: string;
            };
            mediaAttachments?: {
              id: string;
              mimetype: string;
              isPublished: boolean;
              status:
                | 'FAILED_BY_AI'
                | 'FAILED_BY_HUMAN'
                | 'IN_MODERATION'
                | 'PASSED_BY_AI'
                | 'PASSED_BY_HUMAN'
                | 'UNVETTED';
              url: string;
              thumbnailUrl?: string;
            }[];
          };
        };
      };
    };
  };
  postCancelJob: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        jobId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          reason?: {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            customReason?: string;
          };
        } | null;
      };
    };
    responses: {
      /** @description Successful response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            /** Format: date-time */
            createdAt: string;
            description: string;
            /**
             * @description Postcode
             * @example SW1A 1AA
             */
            postcode?: string;
            status:
              | 'REQUEST_ACCEPTED'
              | 'REQUEST_REJECTED'
              | 'SCHEDULED'
              | 'SCHEDULE_CANCELLED'
              | 'DRAFT'
              | 'REQUESTED'
              | 'INTERESTED'
              | 'BOOKED'
              | 'IN_PROGRESS'
              | 'WORK_FINISHED'
              | 'WITHDRAWN'
              | 'COMPLETED'
              | 'CANCELLED'
              | 'V2_CANCELLED';
            preferredStart: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              title: string;
              /** Format: date-time */
              date?: string;
            };
            cancelReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            tradeViewed: boolean;
            tradeMarkedBooked?: boolean;
            fulfilmentType?:
              | 'EMERGENCY_ENQUIRY'
              | 'BOOKING_ENQUIRY'
              | 'ENQUIRY';
            jobRefinementForm: {
              experimentId?: string;
              experimentName?: string;
              variant?: string;
              formId: string;
              questions: {
                questionId: string;
                title: string;
                format: string;
                answers: (
                  | ({
                      key?: string;
                      value: string | null;
                    } & {
                      [key: string]: unknown;
                    })
                  | {
                      key?: string;
                      value: string | null;
                      /** Format: uri */
                      url?: string;
                    }
                )[];
              }[];
            } | null;
            mediaAttachmentIds?: string[];
            booking?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /** Format: date-time */
              createdAt: string;
            };
            details?: {
              questionId: number;
              question: string;
              answerId: number;
              answer: string;
            }[];
            note?: string;
            rejectReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            category: {
              id: number;
              label: string;
            };
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            channelId: string;
            consumer: {
              id: string;
              firstName?: string;
              lastName?: string;
              /** Format: email */
              email?: string;
              phone?: string;
            };
            address: {
              uprn?: string | null;
              city?: string | null;
              postcode: string;
            };
            mediaAttachments?: {
              id: string;
              mimetype: string;
              isPublished: boolean;
              status:
                | 'FAILED_BY_AI'
                | 'FAILED_BY_HUMAN'
                | 'IN_MODERATION'
                | 'PASSED_BY_AI'
                | 'PASSED_BY_HUMAN'
                | 'UNVETTED';
              url: string;
              thumbnailUrl?: string;
            }[];
          };
        };
      };
    };
  };
  postCompleteJob: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        jobId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Successful response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            /** Format: date-time */
            createdAt: string;
            description: string;
            /**
             * @description Postcode
             * @example SW1A 1AA
             */
            postcode?: string;
            status:
              | 'REQUEST_ACCEPTED'
              | 'REQUEST_REJECTED'
              | 'SCHEDULED'
              | 'SCHEDULE_CANCELLED'
              | 'DRAFT'
              | 'REQUESTED'
              | 'INTERESTED'
              | 'BOOKED'
              | 'IN_PROGRESS'
              | 'WORK_FINISHED'
              | 'WITHDRAWN'
              | 'COMPLETED'
              | 'CANCELLED'
              | 'V2_CANCELLED';
            preferredStart: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              title: string;
              /** Format: date-time */
              date?: string;
            };
            cancelReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            tradeViewed: boolean;
            tradeMarkedBooked?: boolean;
            fulfilmentType?:
              | 'EMERGENCY_ENQUIRY'
              | 'BOOKING_ENQUIRY'
              | 'ENQUIRY';
            jobRefinementForm: {
              experimentId?: string;
              experimentName?: string;
              variant?: string;
              formId: string;
              questions: {
                questionId: string;
                title: string;
                format: string;
                answers: (
                  | ({
                      key?: string;
                      value: string | null;
                    } & {
                      [key: string]: unknown;
                    })
                  | {
                      key?: string;
                      value: string | null;
                      /** Format: uri */
                      url?: string;
                    }
                )[];
              }[];
            } | null;
            mediaAttachmentIds?: string[];
            booking?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /** Format: date-time */
              createdAt: string;
            };
            details?: {
              questionId: number;
              question: string;
              answerId: number;
              answer: string;
            }[];
            note?: string;
            rejectReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            category: {
              id: number;
              label: string;
            };
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            channelId: string;
            consumer: {
              id: string;
              firstName?: string;
              lastName?: string;
              /** Format: email */
              email?: string;
              phone?: string;
            };
            address: {
              uprn?: string | null;
              city?: string | null;
              postcode: string;
            };
            mediaAttachments?: {
              id: string;
              mimetype: string;
              isPublished: boolean;
              status:
                | 'FAILED_BY_AI'
                | 'FAILED_BY_HUMAN'
                | 'IN_MODERATION'
                | 'PASSED_BY_AI'
                | 'PASSED_BY_HUMAN'
                | 'UNVETTED';
              url: string;
              thumbnailUrl?: string;
            }[];
          };
        };
      };
    };
  };
  postJobBook: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        jobId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Successful response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            /** Format: date-time */
            createdAt: string;
            description: string;
            /**
             * @description Postcode
             * @example SW1A 1AA
             */
            postcode?: string;
            status:
              | 'REQUEST_ACCEPTED'
              | 'REQUEST_REJECTED'
              | 'SCHEDULED'
              | 'SCHEDULE_CANCELLED'
              | 'DRAFT'
              | 'REQUESTED'
              | 'INTERESTED'
              | 'BOOKED'
              | 'IN_PROGRESS'
              | 'WORK_FINISHED'
              | 'WITHDRAWN'
              | 'COMPLETED'
              | 'CANCELLED'
              | 'V2_CANCELLED';
            preferredStart: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              title: string;
              /** Format: date-time */
              date?: string;
            };
            cancelReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            tradeViewed: boolean;
            tradeMarkedBooked?: boolean;
            fulfilmentType?:
              | 'EMERGENCY_ENQUIRY'
              | 'BOOKING_ENQUIRY'
              | 'ENQUIRY';
            jobRefinementForm: {
              experimentId?: string;
              experimentName?: string;
              variant?: string;
              formId: string;
              questions: {
                questionId: string;
                title: string;
                format: string;
                answers: (
                  | ({
                      key?: string;
                      value: string | null;
                    } & {
                      [key: string]: unknown;
                    })
                  | {
                      key?: string;
                      value: string | null;
                      /** Format: uri */
                      url?: string;
                    }
                )[];
              }[];
            } | null;
            mediaAttachmentIds?: string[];
            booking?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /** Format: date-time */
              createdAt: string;
            };
            details?: {
              questionId: number;
              question: string;
              answerId: number;
              answer: string;
            }[];
            note?: string;
            rejectReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            category: {
              id: number;
              label: string;
            };
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            channelId: string;
            consumer: {
              id: string;
              firstName?: string;
              lastName?: string;
              /** Format: email */
              email?: string;
              phone?: string;
            };
            address: {
              uprn?: string | null;
              city?: string | null;
              postcode: string;
            };
            mediaAttachments?: {
              id: string;
              mimetype: string;
              isPublished: boolean;
              status:
                | 'FAILED_BY_AI'
                | 'FAILED_BY_HUMAN'
                | 'IN_MODERATION'
                | 'PASSED_BY_AI'
                | 'PASSED_BY_HUMAN'
                | 'UNVETTED';
              url: string;
              thumbnailUrl?: string;
            }[];
          };
        };
      };
    };
  };
  postRejectJob: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        jobId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          reason?: {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            customReason?: string;
          };
        } | null;
      };
    };
    responses: {
      /** @description Successful response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            /** Format: date-time */
            createdAt: string;
            description: string;
            /**
             * @description Postcode
             * @example SW1A 1AA
             */
            postcode?: string;
            status:
              | 'REQUEST_ACCEPTED'
              | 'REQUEST_REJECTED'
              | 'SCHEDULED'
              | 'SCHEDULE_CANCELLED'
              | 'DRAFT'
              | 'REQUESTED'
              | 'INTERESTED'
              | 'BOOKED'
              | 'IN_PROGRESS'
              | 'WORK_FINISHED'
              | 'WITHDRAWN'
              | 'COMPLETED'
              | 'CANCELLED'
              | 'V2_CANCELLED';
            preferredStart: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              title: string;
              /** Format: date-time */
              date?: string;
            };
            cancelReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            tradeViewed: boolean;
            tradeMarkedBooked?: boolean;
            fulfilmentType?:
              | 'EMERGENCY_ENQUIRY'
              | 'BOOKING_ENQUIRY'
              | 'ENQUIRY';
            jobRefinementForm: {
              experimentId?: string;
              experimentName?: string;
              variant?: string;
              formId: string;
              questions: {
                questionId: string;
                title: string;
                format: string;
                answers: (
                  | ({
                      key?: string;
                      value: string | null;
                    } & {
                      [key: string]: unknown;
                    })
                  | {
                      key?: string;
                      value: string | null;
                      /** Format: uri */
                      url?: string;
                    }
                )[];
              }[];
            } | null;
            mediaAttachmentIds?: string[];
            booking?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /** Format: date-time */
              createdAt: string;
            };
            details?: {
              questionId: number;
              question: string;
              answerId: number;
              answer: string;
            }[];
            note?: string;
            rejectReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            category: {
              id: number;
              label: string;
            };
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            channelId: string;
            consumer: {
              id: string;
              firstName?: string;
              lastName?: string;
              /** Format: email */
              email?: string;
              phone?: string;
            };
            address: {
              uprn?: string | null;
              city?: string | null;
              postcode: string;
            };
            mediaAttachments?: {
              id: string;
              mimetype: string;
              isPublished: boolean;
              status:
                | 'FAILED_BY_AI'
                | 'FAILED_BY_HUMAN'
                | 'IN_MODERATION'
                | 'PASSED_BY_AI'
                | 'PASSED_BY_HUMAN'
                | 'UNVETTED';
              url: string;
              thumbnailUrl?: string;
            }[];
          };
        };
      };
    };
  };
  postRequestAddress: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        jobId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': Record<string, never>;
        };
      };
    };
  };
  postViewedJob: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        jobId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Successful response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            /** Format: date-time */
            createdAt: string;
            description: string;
            /**
             * @description Postcode
             * @example SW1A 1AA
             */
            postcode?: string;
            status:
              | 'REQUEST_ACCEPTED'
              | 'REQUEST_REJECTED'
              | 'SCHEDULED'
              | 'SCHEDULE_CANCELLED'
              | 'DRAFT'
              | 'REQUESTED'
              | 'INTERESTED'
              | 'BOOKED'
              | 'IN_PROGRESS'
              | 'WORK_FINISHED'
              | 'WITHDRAWN'
              | 'COMPLETED'
              | 'CANCELLED'
              | 'V2_CANCELLED';
            preferredStart: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              title: string;
              /** Format: date-time */
              date?: string;
            };
            cancelReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            tradeViewed: boolean;
            tradeMarkedBooked?: boolean;
            fulfilmentType?:
              | 'EMERGENCY_ENQUIRY'
              | 'BOOKING_ENQUIRY'
              | 'ENQUIRY';
            jobRefinementForm: {
              experimentId?: string;
              experimentName?: string;
              variant?: string;
              formId: string;
              questions: {
                questionId: string;
                title: string;
                format: string;
                answers: (
                  | ({
                      key?: string;
                      value: string | null;
                    } & {
                      [key: string]: unknown;
                    })
                  | {
                      key?: string;
                      value: string | null;
                      /** Format: uri */
                      url?: string;
                    }
                )[];
              }[];
            } | null;
            mediaAttachmentIds?: string[];
            booking?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /** Format: date-time */
              createdAt: string;
            };
            details?: {
              questionId: number;
              question: string;
              answerId: number;
              answer: string;
            }[];
            note?: string;
            rejectReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            category: {
              id: number;
              label: string;
            };
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            channelId: string;
            consumer: {
              id: string;
              firstName?: string;
              lastName?: string;
              /** Format: email */
              email?: string;
              phone?: string;
            };
            address: {
              uprn?: string | null;
              city?: string | null;
              postcode: string;
            };
            mediaAttachments?: {
              id: string;
              mimetype: string;
              isPublished: boolean;
              status:
                | 'FAILED_BY_AI'
                | 'FAILED_BY_HUMAN'
                | 'IN_MODERATION'
                | 'PASSED_BY_AI'
                | 'PASSED_BY_HUMAN'
                | 'UNVETTED';
              url: string;
              thumbnailUrl?: string;
            }[];
          };
        };
      };
    };
  };
  putJobNote: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        /** @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com */
        jobId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          note: string;
        };
      };
    };
    responses: {
      /** @description Successful response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            /** Format: date-time */
            createdAt: string;
            description: string;
            /**
             * @description Postcode
             * @example SW1A 1AA
             */
            postcode?: string;
            status:
              | 'REQUEST_ACCEPTED'
              | 'REQUEST_REJECTED'
              | 'SCHEDULED'
              | 'SCHEDULE_CANCELLED'
              | 'DRAFT'
              | 'REQUESTED'
              | 'INTERESTED'
              | 'BOOKED'
              | 'IN_PROGRESS'
              | 'WORK_FINISHED'
              | 'WITHDRAWN'
              | 'COMPLETED'
              | 'CANCELLED'
              | 'V2_CANCELLED';
            preferredStart: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              title: string;
              /** Format: date-time */
              date?: string;
            };
            cancelReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            tradeViewed: boolean;
            tradeMarkedBooked?: boolean;
            fulfilmentType?:
              | 'EMERGENCY_ENQUIRY'
              | 'BOOKING_ENQUIRY'
              | 'ENQUIRY';
            jobRefinementForm: {
              experimentId?: string;
              experimentName?: string;
              variant?: string;
              formId: string;
              questions: {
                questionId: string;
                title: string;
                format: string;
                answers: (
                  | ({
                      key?: string;
                      value: string | null;
                    } & {
                      [key: string]: unknown;
                    })
                  | {
                      key?: string;
                      value: string | null;
                      /** Format: uri */
                      url?: string;
                    }
                )[];
              }[];
            } | null;
            mediaAttachmentIds?: string[];
            booking?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              /** Format: date-time */
              createdAt: string;
            };
            details?: {
              questionId: number;
              question: string;
              answerId: number;
              answer: string;
            }[];
            note?: string;
            rejectReason?: {
              /**
               * Format: uuid
               * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
               */
              id: string;
              reason: string;
            };
            category: {
              id: number;
              label: string;
            };
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            channelId: string;
            consumer: {
              id: string;
              firstName?: string;
              lastName?: string;
              /** Format: email */
              email?: string;
              phone?: string;
            };
            address: {
              uprn?: string | null;
              city?: string | null;
              postcode: string;
            };
            mediaAttachments?: {
              id: string;
              mimetype: string;
              isPublished: boolean;
              status:
                | 'FAILED_BY_AI'
                | 'FAILED_BY_HUMAN'
                | 'IN_MODERATION'
                | 'PASSED_BY_AI'
                | 'PASSED_BY_HUMAN'
                | 'UNVETTED';
              url: string;
              thumbnailUrl?: string;
            }[];
          };
        };
      };
    };
  };
  getCancelReasons: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            reason: string;
            customReason?: boolean;
          }[];
        };
      };
    };
  };
  getRejectReasons: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            id: string;
            reason: string;
            customReason?: boolean;
          }[];
        };
      };
    };
  };
  getActivities: {
    parameters: {
      query: {
        pageNumber: number;
        pageSize: number;
        orderBy?: string;
        orderDirection?: 'ASC' | 'DESC';
        status?:
          | 'EXPIRED'
          | 'COMPLETED'
          | 'PAYMENT_PENDING'
          | 'REFUNDED'
          | 'AUTHORIZED'
          | 'CANCELLED'
          | 'PAID'
          | 'CHARGEBACK_REVERSED'
          | 'CHARGEBACK'
          | 'PAYOUT'
          | 'TOPUP';
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            pagination: {
              total?: number;
              pageCount?: number;
              page: number;
              size: number;
              hasNext?: boolean;
              hasPrevious?: boolean;
            };
            data: {
              companyId: string;
              entityId: string;
              paymentLinkId: string | null;
              amount: number;
              consumerName: string | null;
              reference: string | null;
              description: string | null;
              type:
                | 'EXPIRED'
                | 'COMPLETED'
                | 'PAYMENT_PENDING'
                | 'REFUNDED'
                | 'AUTHORIZED'
                | 'CANCELLED'
                | 'PAID'
                | 'CHARGEBACK_REVERSED'
                | 'CHARGEBACK'
                | 'PAYOUT'
                | 'TOPUP';
              /** Format: date-time */
              date: string;
            }[];
            counts: {
              EXPIRED: number;
              COMPLETED: number;
              PAYMENT_PENDING: number;
              REFUNDED: number;
              AUTHORIZED: number;
              CANCELLED: number;
              PAID: number;
              CHARGEBACK_REVERSED: number;
              CHARGEBACK: number;
              PAYOUT: number;
              TOPUP: number;
            };
          };
        };
      };
    };
  };
  getActivitiesStatementReport: {
    parameters: {
      query: {
        pageNumber: number;
        pageSize: number;
        fromDate: string;
        toDate: string;
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  getBalance: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            balance: {
              currency: string;
              balance: number;
              available: number;
              pending?: number;
              reserved: number;
            };
            outstandingBalance: number;
          };
        };
      };
    };
  };
  getBalanceAccountIds: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            balanceAccountId: string;
            merchantAccountId: string;
          };
        };
      };
    };
  };
  getExternalTaxInformation: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            traderType?: 0 | 1 | 2 | 3 | 4 | 5;
            taxIdentifiers: {
              companyRegistrationNumber?: string;
              vatNumber?: string;
              vatAbsenceReason?: string;
            };
          };
        };
      };
    };
  };
  getOnboardingInformation: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            onboarding: {
              providedTaxInformation: boolean;
              progress: number;
              status:
                | 'NOT_ENROLLED'
                | 'INVITED_TO_ENROLL'
                | 'ENROLLED'
                | 'REQUIRES_FURTHER_ACTION'
                | 'READY_FOR_PAYMENT'
                | 'RE_ENROLL'
                | 'DISABLED';
              ecommerceEnabled?: boolean | null;
              posEnabled?: boolean | null;
            };
            problems: string[] | null;
            anyDisabledCapabilities: boolean;
            capabilities: {
              RECEIVE_FROM_PLATFORM_PAYMENTS: {
                status: 'INVALID' | 'PENDING' | 'REJECTED' | 'VALID';
                disabled: boolean;
              } | null;
              RECEIVE_FROM_BALANCE_ACCOUNT: {
                status: 'INVALID' | 'PENDING' | 'REJECTED' | 'VALID';
                disabled: boolean;
              } | null;
              SEND_TO_BALANCE_ACCOUNT: {
                status: 'INVALID' | 'PENDING' | 'REJECTED' | 'VALID';
                disabled: boolean;
              } | null;
              SEND_TO_TRANSFER_INSTRUMENT: {
                status: 'INVALID' | 'PENDING' | 'REJECTED' | 'VALID';
                disabled: boolean;
              } | null;
              RECEIVE_FROM_TRANSFER_INSTRUMENT: {
                status: 'INVALID' | 'PENDING' | 'REJECTED' | 'VALID';
                disabled: boolean;
              } | null;
              RECEIVE_PAYMENTS: {
                status: 'INVALID' | 'PENDING' | 'REJECTED' | 'VALID';
                disabled: boolean;
              } | null;
            };
          };
        };
      };
    };
  };
  getPaymentMethods: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            paymentMethods?: {
              apps?: {
                id: string;
              }[];
              brand?: string;
              brands?: string[];
              configuration?: {
                [key: string]: string;
              };
              fundingSource?: 'credit' | 'debit';
              group?: {
                name?: string;
                paymentMethodData?: string;
                type?: string;
              };
              inputDetails?: {
                configuration?: {
                  [key: string]: string;
                };
                details?: {
                  configuration?: {
                    [key: string]: string;
                  };
                  items?: {
                    id?: string;
                    name?: string;
                  }[];
                  key?: string;
                  optional?: boolean;
                  type?: string;
                  value?: string;
                }[];
                inputDetails?: {
                  configuration?: {
                    [key: string]: string;
                  };
                  items?: {
                    id?: string;
                    name?: string;
                  }[];
                  key?: string;
                  optional?: boolean;
                  type?: string;
                  value?: string;
                }[];
                itemSearchUrl?: string;
                items?: {
                  id?: string;
                  name?: string;
                }[];
                key?: string;
                optional?: boolean;
                type?: string;
                value?: string;
              }[];
              issuers?: {
                disabled?: boolean;
                id?: string;
              }[];
              name?: string;
              type?: string;
            }[];
          };
        };
      };
    };
  };
  getPaymentRequest: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        paymentLinkId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            status:
              | 'PAYMENT_PENDING'
              | 'AUTHORIZED'
              | 'PAID'
              | 'EXPIRED'
              | 'REFUNDED'
              | 'CANCELLED'
              | 'CHARGEBACK'
              | 'CHARGEBACK_REVERSED';
            paymentType?:
              | ('PAYMENT_LINK' | 'TAP_TO_PAY' | 'PAY_BY_PHONE')
              | null;
            reference: string;
            description?: string | null;
            jobReference?: string | null;
            /** Format: date-time */
            requestCreatedAt: string;
            totalAmount: number;
            commissionAmount: number;
            tradeAmount: number;
            companyId: string;
            jobId?: string;
            jobTitle: string;
            opportunityId?: string;
            consumerId?: string;
            consumerName: string;
            consumerEmail: string;
            paymentLinkId: string;
            paymentUrl: string;
            checkoutUrl?: string;
            /** Format: date-time */
            expiryDate: string;
            /** Format: date-time */
            dueDate: string;
            currency: string;
          };
        };
      };
    };
  };
  getPaymentRequestsByOpportunity: {
    parameters: {
      query: {
        pageNumber: number;
        pageSize: number;
        orderBy?: string;
        orderDirection?: 'ASC' | 'DESC';
      };
      header: {
        'x-trade-company-id': number;
      };
      path: {
        opportunityId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            pagination: {
              total?: number;
              pageCount?: number;
              page: number;
              size: number;
              hasNext?: boolean;
              hasPrevious?: boolean;
            };
            data: {
              id: string;
              companyId: string;
              jobId?: string;
              jobReference?: string;
              description?: string;
              reference: string;
              consumerId?: string;
              consumerEmail: string;
              paymentLinkId: string;
              opportunityId?: string;
              quoteId?: string;
              paymentUrl: string | null;
              checkoutUrl?: string | null;
              /** Format: date-time */
              expiryDate: string;
              /** Format: date-time */
              dueDate: string;
              /** Format: date-time */
              createdAt: string;
              /** Format: date-time */
              updatedAt: string;
              status:
                | 'PAYMENT_PENDING'
                | 'AUTHORIZED'
                | 'PAID'
                | 'EXPIRED'
                | 'REFUNDED'
                | 'CANCELLED'
                | 'CHARGEBACK'
                | 'CHARGEBACK_REVERSED';
              totalAmount: number;
              tradeAmount: number;
              commissionAmount: number;
              currency: string;
            }[];
          };
        };
      };
    };
  };
  getPaymentRequestsByQuote: {
    parameters: {
      query: {
        pageNumber: number;
        pageSize: number;
        orderBy?: string;
        orderDirection?: 'ASC' | 'DESC';
      };
      header: {
        'x-trade-company-id': number;
      };
      path: {
        quoteId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            pagination: {
              total?: number;
              pageCount?: number;
              page: number;
              size: number;
              hasNext?: boolean;
              hasPrevious?: boolean;
            };
            data: {
              id: string;
              companyId: string;
              jobId?: string;
              jobReference?: string;
              description?: string;
              reference: string;
              consumerId?: string;
              consumerEmail: string;
              paymentLinkId: string;
              opportunityId?: string;
              quoteId?: string;
              paymentUrl: string | null;
              checkoutUrl?: string | null;
              /** Format: date-time */
              expiryDate: string;
              /** Format: date-time */
              dueDate: string;
              /** Format: date-time */
              createdAt: string;
              /** Format: date-time */
              updatedAt: string;
              status:
                | 'PAYMENT_PENDING'
                | 'AUTHORIZED'
                | 'PAID'
                | 'EXPIRED'
                | 'REFUNDED'
                | 'CANCELLED'
                | 'CHARGEBACK'
                | 'CHARGEBACK_REVERSED';
              totalAmount: number;
              tradeAmount: number;
              commissionAmount: number;
              currency: string;
            }[];
          };
        };
      };
    };
  };
  getPaymentRequestsQuoteMetrics: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        quoteId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            hasProcessedPayments: boolean;
            totalPaid: number;
            totalDue: number;
            totalOutstanding: number;
          };
        };
      };
    };
  };
  getSplitPayment: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        penceAmount: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            serviceCharge: number;
            total: number;
          };
        };
      };
    };
  };
  getTaxInformation: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            traderType: 0 | 1 | 2 | 3 | 4;
            vatRegistrationNo?: string;
          };
        };
      };
    };
  };
  postTaxInformation: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          vatNumber?: string;
          uniqueTaxpayerReference: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': Record<string, never>;
        };
      };
    };
  };
  cancelPaymentRequest: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        paymentLinkId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          reason: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': Record<string, never>;
        };
      };
    };
  };
  postOnboard: {
    parameters: {
      query: {
        clientType: 'WEB' | 'MOBILE';
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          primaryCategoryId: number;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            type: 'soleProprietorship' | 'organisation';
            legalName: string;
            vatNumber?: string;
            registeredAddress: {
              city: string;
              country: string;
              postalCode: string;
              street: string;
            };
            phoneNumber: string;
            individual?: {
              residentialAddress: {
                /** @default GB */
                country: string;
                city: string;
                postalCode: string;
                street: string;
              };
              name: {
                firstName: string;
                lastName: string;
              };
              birthData?: {
                dateOfBirth: string;
              };
              /** Format: email */
              email?: string;
              phone: {
                number: string;
              };
            };
            traderType: 0 | 1 | 2 | 3 | 4;
            hostedOnboardingUrl: string;
          };
        };
      };
    };
  };
  postPayByPhonePaymentDetails: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          /** @enum {string} */
          vendor: 'adyen';
          vendorData: unknown;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @enum {string} */
            vendor: 'adyen';
            vendorData: unknown;
          };
        };
      };
    };
  };
  postCreatePayByPhonePaymentRequest: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          paymentRequest: {
            amount: {
              currency: string;
              value: number;
            };
            description?: string;
            /** Format: date-time */
            dueDate: string;
            jobId?: string;
            /** @default JOB_SERVICE */
            jobSource?: 'JOB_SERVICE' | 'SECURE_CONTACT' | 'OFF_PLATFORM';
            jobReference?: string;
            opportunityId?: string;
            quoteId?: string;
            companyId: string;
            consumerId?: string;
            firstName: string;
            lastName: string;
            /** Format: email */
            emailAddress: string;
            billingAddress?: {
              houseNumberOrName: string;
              street: string;
              city: string;
              postalCode: string;
              country: string;
            };
            deliveryAddress?: {
              houseNumberOrName: string;
              street: string;
              city: string;
              postalCode: string;
              country: string;
            };
            splits: {
              amount: {
                currency: string;
                value: number;
              };
              type: 'TRADER' | 'CHECKATRADE';
            }[];
            tokenData?: {
              company: {
                companyId: number;
                companyName: string;
              };
              job: {
                categoryId: string;
                description: string;
                postcode: string;
              };
              consumer: {
                firstName: string;
                lastName: string;
                phoneNumber: string;
                emailAddress: string;
              };
            };
          };
          /** @enum {string} */
          vendor: 'adyen';
          vendorData: unknown;
          job?: {
            categoryId: string;
            description: string;
            postcode: string;
          };
          consumer?: {
            firstName: string;
            lastName: string;
            phoneNumber: string;
            emailAddress: string;
          };
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @enum {string} */
            vendor: 'adyen';
            vendorData: unknown;
            paymentLinkId?: string;
            paymentId?: string;
          };
        };
      };
    };
  };
  postCreatePaymentRequest: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          paymentRequest: {
            amount: {
              currency: string;
              value: number;
            };
            description?: string;
            /** Format: date-time */
            dueDate: string;
            /** @default JOB_SERVICE */
            jobSource?: 'JOB_SERVICE' | 'SECURE_CONTACT' | 'OFF_PLATFORM';
            jobReference?: string;
            quoteId?: string;
            companyId: string;
            consumerId?: string;
            firstName: string;
            lastName: string;
            /** Format: email */
            emailAddress: string;
            billingAddress?: {
              houseNumberOrName: string;
              street: string;
              city: string;
              postalCode: string;
              country: string;
            };
            deliveryAddress?: {
              houseNumberOrName: string;
              street: string;
              city: string;
              postalCode: string;
              country: string;
            };
            splits: {
              amount: {
                currency: string;
                value: number;
              };
              type: 'TRADER' | 'CHECKATRADE';
            }[];
            tokenData?: {
              company: {
                companyId: number;
                companyName: string;
              };
              job: {
                categoryId: string;
                description: string;
                postcode: string;
              };
              consumer: {
                firstName: string;
                lastName: string;
                phoneNumber: string;
                emailAddress: string;
              };
            };
            /** Format: uuid */
            jobId: string;
            opportunityId: string;
          };
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            link: {
              id: string;
              status:
                | 'PAYMENT_PENDING'
                | 'AUTHORIZED'
                | 'PAID'
                | 'EXPIRED'
                | 'REFUNDED'
                | 'CANCELLED'
                | 'CHARGEBACK'
                | 'CHARGEBACK_REVERSED';
              url: string;
            };
            paymentRequest: {
              currency: string;
              totalAmount: number;
              tradeAmount: number;
              commissionAmount: number;
              companyId: string;
              /** @default PAYMENT_LINK */
              paymentType: 'PAYMENT_LINK' | 'TAP_TO_PAY' | 'PAY_BY_PHONE';
              /** @default JOB_SERVICE */
              jobSource: 'JOB_SERVICE' | 'SECURE_CONTACT' | 'OFF_PLATFORM';
              /** Format: uuid */
              jobId?: string;
              jobReference?: string;
              description?: string;
              opportunityId?: string;
              quoteId?: string;
              reason: string | null;
              /** Format: uuid */
              consumerId?: string;
              consumerName: string;
              /** Format: email */
              consumerEmail: string;
              paymentLinkId?: string;
              paymentUrl: string | null;
              checkoutUrl?: string | null;
              reference: string;
              status:
                | 'PAYMENT_PENDING'
                | 'AUTHORIZED'
                | 'PAID'
                | 'EXPIRED'
                | 'REFUNDED'
                | 'CANCELLED'
                | 'CHARGEBACK'
                | 'CHARGEBACK_REVERSED';
              /** Format: date-time */
              expiryDate: string;
              /** Format: date-time */
              dueDate: string;
              /** Format: date-time */
              createdAt: string;
              /** Format: date-time */
              updatedAt: string;
            };
            /** Format: uuid */
            paymentId: string;
          };
        };
      };
    };
  };
  postCreatePaymentRequestOffPlatformJob: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          paymentRequest: {
            amount: {
              currency: string;
              value: number;
            };
            description?: string;
            /** Format: date-time */
            dueDate: string;
            jobId?: string;
            /** @default JOB_SERVICE */
            jobSource?: 'JOB_SERVICE' | 'SECURE_CONTACT' | 'OFF_PLATFORM';
            jobReference?: string;
            opportunityId?: string;
            quoteId?: string;
            companyId: string;
            consumerId?: string;
            firstName: string;
            lastName: string;
            /** Format: email */
            emailAddress: string;
            billingAddress?: {
              houseNumberOrName: string;
              street: string;
              city: string;
              postalCode: string;
              country: string;
            };
            deliveryAddress?: {
              houseNumberOrName: string;
              street: string;
              city: string;
              postalCode: string;
              country: string;
            };
            splits: {
              amount: {
                currency: string;
                value: number;
              };
              type: 'TRADER' | 'CHECKATRADE';
            }[];
            tokenData?: {
              company: {
                companyId: number;
                companyName: string;
              };
              job: {
                categoryId: string;
                description: string;
                postcode: string;
              };
              consumer: {
                firstName: string;
                lastName: string;
                phoneNumber: string;
                emailAddress: string;
              };
            };
          };
          job: {
            categoryId: string;
            description: string;
            postcode: string;
          };
          consumer: {
            firstName: string;
            lastName: string;
            phoneNumber: string;
            emailAddress: string;
          };
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            link: {
              id: string;
              status:
                | 'PAYMENT_PENDING'
                | 'AUTHORIZED'
                | 'PAID'
                | 'EXPIRED'
                | 'REFUNDED'
                | 'CANCELLED'
                | 'CHARGEBACK'
                | 'CHARGEBACK_REVERSED';
              url: string;
            };
            paymentRequest: {
              currency: string;
              totalAmount: number;
              tradeAmount: number;
              commissionAmount: number;
              companyId: string;
              /** @default PAYMENT_LINK */
              paymentType: 'PAYMENT_LINK' | 'TAP_TO_PAY' | 'PAY_BY_PHONE';
              /** @default JOB_SERVICE */
              jobSource: 'JOB_SERVICE' | 'SECURE_CONTACT' | 'OFF_PLATFORM';
              /** Format: uuid */
              jobId?: string;
              jobReference?: string;
              description?: string;
              opportunityId?: string;
              quoteId?: string;
              reason: string | null;
              /** Format: uuid */
              consumerId?: string;
              consumerName: string;
              /** Format: email */
              consumerEmail: string;
              paymentLinkId?: string;
              paymentUrl: string | null;
              checkoutUrl?: string | null;
              reference: string;
              status:
                | 'PAYMENT_PENDING'
                | 'AUTHORIZED'
                | 'PAID'
                | 'EXPIRED'
                | 'REFUNDED'
                | 'CANCELLED'
                | 'CHARGEBACK'
                | 'CHARGEBACK_REVERSED';
              /** Format: date-time */
              expiryDate: string;
              /** Format: date-time */
              dueDate: string;
              /** Format: date-time */
              createdAt: string;
              /** Format: date-time */
              updatedAt: string;
            };
            /** Format: uuid */
            paymentId: string;
          };
        };
      };
    };
  };
  postTerminalSession: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          setupToken: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            sdkData?: string;
          };
        };
      };
    };
  };
  getReferralCode: {
    parameters: {
      query: {
        campaignId: number;
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            code: string;
            campaignId: number;
            /** Format: uri */
            url: string;
            sharing: {
              social: string;
              /** Format: uri */
              url: string;
            }[];
          };
        };
      };
    };
  };
  getZuoraAccount: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            updated_by_id: string;
            updated_time: string;
            created_by_id: string;
            created_time: string;
            account_number: string;
            name: string;
            description?: string;
            default_payment_method_id?: string;
            bill_cycle_day?: number;
          };
        };
      };
    };
  };
  getZuoraPaymentMethod: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        paymentMethodId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            updated_by_id: string;
            updated_time: string;
            created_by_id: string;
            created_time: string;
            account_id: string;
            billing_details?: {
              name?: string;
              address?: {
                line1?: string;
                line2?: string;
                city?: string;
                country?: string;
                state?: string;
                postal_code?: string;
              };
              email?: string;
              phone?: string;
            };
            bacs_debit?: {
              account_number: string;
              bank_code: string;
              mandate?: {
                id: string;
                reason?: string;
                state?: string;
              };
            };
            state?: string;
            existing_mandate?: boolean;
            total_number_of_processed_payments?: number;
            total_number_of_error_payments?: number;
            last_transaction_time?: string;
            last_transaction_status?: string;
          };
        };
      };
    };
  };
  getZuoraStatus: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            defaultPaymentMethodState: 'active' | 'closed' | 'scrubbed';
          };
        };
      };
    };
  };
  patchZuoraAccount: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        accountId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          paymentMethodId: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            updated_by_id: string;
            updated_time: string;
            created_by_id: string;
            created_time: string;
            account_number: string;
            name: string;
            description?: string;
            default_payment_method_id?: string;
            bill_cycle_day?: number;
          };
        };
      };
    };
  };
  postZuoraRsaSignature: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            key: string;
            signature: string;
            tenantId: string;
            token: string;
            success: boolean;
          };
        };
      };
      /** @description Default Response */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            type: string;
            title: string;
            detail: string;
            instance: string;
            status: number;
          };
        };
      };
      /** @description Default Response */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            type: string;
            title: string;
            detail: string;
            instance: string;
            status: number;
          };
        };
      };
      /** @description Default Response */
      500: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            type: string;
            title: string;
            detail: string;
            instance: string;
            status: number;
          };
        };
      };
    };
  };
  addTeamPersons: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          firstName: string;
          lastName: string;
          nickname?: string;
          /** Format: date-time */
          dateOfBirth: string;
          address: {
            line1: string | null;
            line2: string | null;
            city: string | null;
            county: string | null;
            postalCode: string | null;
          };
          email: string;
          phoneNumber: string;
          workCategories: {
            id: string;
            subCategories: {
              id: string;
            }[];
          }[];
          contractingType: 'Employee' | 'Subcontractor';
          id?: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
          };
        };
      };
    };
  };
  getTeamPerson: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        personId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            firstName: string;
            lastName: string;
            dateOfBirth: string;
            email: string;
            phoneNumber: string;
            address: {
              line1: string | null;
              line2: string | null;
              city: string | null;
              county: string | null;
              postalCode: string | null;
            };
            category: {
              id: string;
              name: string;
              subCategories: {
                id: string;
                name: string;
                accreditationsNeeded?: {
                  id: number;
                  deleted: boolean;
                  name: string;
                }[];
              }[];
            }[];
            vettingStatus:
              | 'ISSUE'
              | 'IN_PROGRESS'
              | 'NOT_STARTED'
              | 'ACTIVE'
              | 'CONSENT_PENDING';
            contractingType: 'Employee' | 'Subcontractor';
          };
        };
      };
      /** @description Default Response */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      500: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  deleteTeamPerson: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        personId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          type: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
          };
        };
      };
    };
  };
  getTeamPersons: {
    parameters: {
      query: {
        contractingType: 'Employee' | 'Subcontractor';
        searchTerm?: string;
        /** @description Page number */
        page?: number;
        pageSize?: number;
        sortBy?: string;
        sortOrder?: string;
        status?: string;
        /** @description Size of the page */
        size?: number;
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            data: {
              id: string;
              fullName: string;
              firstName: string;
              lastName: string;
              dateOfBirth: string;
              lastUpdated: string;
              email: string;
              mobilePhone?: null | string;
              phone?: null | string;
              role: 'Owner' | 'Director' | 'Admin Contact' | 'Employee';
              workCategories?: {
                id: string;
                subCategories: {
                  id: string;
                }[];
              }[];
              vettingStatus?:
                | 'IN_PROGRESS'
                | 'NOT_STARTED'
                | 'ACTIVE'
                | 'FAILED'
                | 'NEED_MORE_INFO'
                | 'CONSENT_PENDING';
              consentStatus?:
                | 'NotCreated'
                | 'NotProvided'
                | 'Granted'
                | 'Denied';
            }[];
            pages: number;
            total: number;
          };
        };
      };
      /** @description Default Response */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      422: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      500: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  getTeamInvite: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Successful response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            relationshipStatus: ('Pending' | 'Accepted') | null;
            expiryDate: string;
            /** @enum {string} */
            relationshipType: 'Subcontractor';
            emailAddress: string;
            dateCreated: string;
            dateUpdated: string;
            dateAccepted?: string;
            childMemberId: string | null;
            parentMemberId: string;
            currentMemberId: string;
            parentCompanyInfo: {
              name: string;
              membershipType:
                | 'Full Member'
                | 'Affiliate Member'
                | 'Non Member'
                | 'Ex-Member'
                | 'Claimed'
                | 'Paused'
                | 'National Accounts'
                | 'Free Member'
                | 'Essentials Member';
            } | null;
          };
        };
      };
    };
  };
  putTeamInvite: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          relationshipStatus: 'Pending' | 'Accepted';
          expiryDate?: string;
          hasAccepted?: boolean;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            relationshipStatus: ('Pending' | 'Accepted') | null;
            expiryDate: string;
            relationshipType: 'Subcontractor' & 'Subcontractor';
            emailAddress: string;
            dateCreated: string;
            dateUpdated: string;
            dateAccepted?: string;
            childMemberId: string | null;
            parentMemberId: string;
          };
        };
      };
    };
  };
  getTeamInvites: {
    parameters: {
      query?: {
        /** @description Page number */
        page?: number;
        pageSize?: number;
        /** @description Size of the page */
        size?: number;
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            data: {
              invite: {
                id: string;
                relationshipStatus: ('Pending' | 'Accepted') | null;
                expiryDate: string;
                /** @enum {string} */
                relationshipType: 'Subcontractor';
                emailAddress: string;
                dateCreated: string;
                dateUpdated: string;
                dateAccepted?: string;
                childMemberId: string | null;
                parentMemberId: string;
                vettingStatus:
                  | 'IN_PROGRESS'
                  | 'NOT_STARTED'
                  | 'ACTIVE'
                  | 'FAILED'
                  | 'NEED_MORE_INFO'
                  | 'CONSENT_PENDING';
              };
              childCompany: {
                companyId: number;
                traderId: number;
                name: string;
                uniqueName: string;
                id: string;
              };
            }[];
            pages: number;
            total: number;
          };
        };
      };
      /** @description Default Response */
      500: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  inviteSubcontractor: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          email: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            inviteUrl: string;
          };
        };
      };
    };
  };
  getTeamPersonsVettingDetails: {
    parameters: {
      query: {
        email: string;
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            vettingId: string;
            externalId: string;
            consentStatus: 'NotCreated' | 'NotProvided' | 'Granted' | 'Denied';
            vettingStatus:
              | 'IN_PROGRESS'
              | 'NOT_STARTED'
              | 'ACTIVE'
              | 'FAILED'
              | 'NEED_MORE_INFO'
              | 'CONSENT_PENDING';
          };
        };
      };
      /** @description Default Response */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      422: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      500: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  putTeamPerson: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        personId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          firstName: string;
          lastName: string;
          nickname?: string;
          /** Format: date-time */
          dateOfBirth: string;
          address: {
            line1: string | null;
            line2: string | null;
            city: string | null;
            county: string | null;
            postalCode: string | null;
          };
          email: string;
          phoneNumber: string;
          mobilePhone?: string;
          workCategories: {
            id: string;
            subCategories: {
              id: string;
            }[];
          }[];
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
          };
        };
      };
    };
  };
  addConsentEmailEvent: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        personId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          email: string;
          phoneNumber: string;
          firstName: string;
          employer: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            Email: string;
          };
        };
      };
      /** @description Default Response */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      500: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  putTeamPersonVetting: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        personId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          consent: 'Granted' | 'Denied';
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            vettingId?: string | null;
            externalId?: string | null;
            consentStatus: 'NotCreated' | 'NotProvided' | 'Granted' | 'Denied';
          };
        };
      };
    };
  };
  getTeamCounters: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            totalEmployees: number;
            totalSubcontractors: number;
            totalInvites: number;
          };
        };
      };
    };
  };
  getTeamPendingInvites: {
    parameters: {
      query?: {
        /** @description Page number */
        page?: number;
        pageSize?: number;
        relationshipStatus?: 'Pending' | 'Accepted';
        /** @description Size of the page */
        size?: number;
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            data: {
              invite: {
                id: string;
                relationshipStatus: ('Pending' | 'Accepted') | null;
                expiryDate: string;
                /** @enum {string} */
                relationshipType: 'Subcontractor';
                emailAddress: string;
                dateCreated: string;
                dateUpdated: string;
                dateAccepted?: string;
                childMemberId: string | null;
                parentMemberId: string;
              };
              parentCompany: {
                companyId: number;
                traderId: number;
                name: string;
                uniqueName: string;
                id: string;
              };
            }[];
            pages: number;
            total: number;
          };
        };
      };
      /** @description Default Response */
      500: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  getVettingConsentTokenDecode: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
        'x-trade-time-limited-token': string;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            personId: string;
            memberId: string;
            companyId: number;
          };
        };
      };
      /** @description Default Response */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      500: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  deleteSubcontractor: {
    parameters: {
      query: {
        inviteId: string;
      };
      header: {
        'x-trade-company-id': number;
      };
      path: {
        subcontractorId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            subcontractorId: string;
            memberId: string;
            inviteId: string;
          };
        };
      };
    };
  };
  getTeamVettingStatus: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            vettingStatus:
              | 'IN_PROGRESS'
              | 'NOT_STARTED'
              | 'ACTIVE'
              | 'FAILED'
              | 'NEED_MORE_INFO'
              | 'CONSENT_PENDING';
          };
        };
      };
      /** @description Default Response */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      422: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      500: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  getIsFullMemberOrNationalAccount: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Indicates whether the member is a full member or a national account */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': boolean;
        };
      };
    };
  };
  getPersonAccreditations: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        personId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            accreditationId: number;
            personId: string;
            companyId: number;
            accreditationName?: string;
            accreditationLogo?: string;
            canExpire: boolean;
            status:
              | 'Active'
              | 'Rejected'
              | 'Expires Soon'
              | 'Expired'
              | 'Pending Review'
              | 'Further Action Required';
            proof?: {
              fileName: string;
              fileSize: number;
              reference: string;
              mimeType: string;
              /** Format: date-time */
              uploadedDate: string;
            }[];
            /** Format: date-time */
            statusDate: string;
            statusText: string;
          }[];
        };
      };
    };
  };
  postAccreditation: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        personId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          accreditationId: number;
          /** Format: date-time */
          expiryDate?: string;
          proof?: {
            fileName: string;
            fileSize: number;
            reference: string;
            mimeType: string;
            /** Format: date-time */
            uploadedDate: string;
          }[];
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            accreditationId: number;
            personId: string;
            companyId: number;
            accreditationName?: string;
            accreditationLogo?: string;
            canExpire: boolean;
            status:
              | 'Active'
              | 'Rejected'
              | 'Expires Soon'
              | 'Expired'
              | 'Pending Review'
              | 'Further Action Required';
            proof?: {
              fileName: string;
              fileSize: number;
              reference: string;
              mimeType: string;
              /** Format: date-time */
              uploadedDate: string;
            }[];
            /** Format: date-time */
            statusDate: string;
            statusText: string;
          };
        };
      };
    };
  };
  getPersonAccreditation: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        accreditationId: number;
        personId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            accreditationId: number;
            personId: string;
            companyId: number;
            accreditationName?: string;
            accreditationLogo?: string;
            canExpire: boolean;
            status:
              | 'Active'
              | 'Rejected'
              | 'Expires Soon'
              | 'Expired'
              | 'Pending Review'
              | 'Further Action Required';
            proof?: {
              fileName: string;
              fileSize: number;
              reference: string;
              mimeType: string;
              /** Format: date-time */
              uploadedDate: string;
            }[];
            /** Format: date-time */
            statusDate: string;
            statusText: string;
          };
        };
      };
    };
  };
  deletePersonAccreditation: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        id: number;
        personId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  getPersonRequiredAccreditations: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        personId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            accreditationIds: number[];
          };
        };
      };
    };
  };
  updateCompanyName: {
    parameters: {
      query?: never;
      header: {
        /** @description UUIDv7 */
        'x-correlation-id'?: string;
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          companyName: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            requestType:
              | 'CompanyName'
              | 'MemberEmail'
              | 'MemberPhone'
              | 'MemberTradingAddress'
              | 'MemberAdminAddress'
              | 'AddPerson'
              | 'UpdatePerson'
              | 'RemovePerson';
            correlationId?: string;
          };
        };
      };
      /** @description Default Response */
      403: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  updateMmeberEmail: {
    parameters: {
      query?: never;
      header: {
        /** @description UUIDv7 */
        'x-correlation-id'?: string;
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          /** Format: email */
          memberEmail: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            requestType:
              | 'CompanyName'
              | 'MemberEmail'
              | 'MemberPhone'
              | 'MemberTradingAddress'
              | 'MemberAdminAddress'
              | 'AddPerson'
              | 'UpdatePerson'
              | 'RemovePerson';
            correlationId?: string;
          };
        };
      };
    };
  };
  updateMmeberPhone: {
    parameters: {
      query?: never;
      header: {
        /** @description UUIDv7 */
        'x-correlation-id'?: string;
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          memberPhone: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            requestType:
              | 'CompanyName'
              | 'MemberEmail'
              | 'MemberPhone'
              | 'MemberTradingAddress'
              | 'MemberAdminAddress'
              | 'AddPerson'
              | 'UpdatePerson'
              | 'RemovePerson';
            correlationId?: string;
          };
        };
      };
    };
  };
  updateMemberTradingAddress: {
    parameters: {
      query?: never;
      header: {
        /** @description UUIDv7 */
        'x-correlation-id'?: string;
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          street: string;
          postcode: string;
          city: string;
          town?: string | null;
          county?: string | null;
          country?: string | null;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            requestType:
              | 'CompanyName'
              | 'MemberEmail'
              | 'MemberPhone'
              | 'MemberTradingAddress'
              | 'MemberAdminAddress'
              | 'AddPerson'
              | 'UpdatePerson'
              | 'RemovePerson';
            correlationId?: string;
          };
        };
      };
    };
  };
  updateMemberAdminAddress: {
    parameters: {
      query?: never;
      header: {
        /** @description UUIDv7 */
        'x-correlation-id'?: string;
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          street: string;
          postcode: string;
          city: string;
          town?: string | null;
          county?: string | null;
          country?: string | null;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            requestType:
              | 'CompanyName'
              | 'MemberEmail'
              | 'MemberPhone'
              | 'MemberTradingAddress'
              | 'MemberAdminAddress'
              | 'AddPerson'
              | 'UpdatePerson'
              | 'RemovePerson';
            correlationId?: string;
          };
        };
      };
    };
  };
  updatePerson: {
    parameters: {
      query?: never;
      header: {
        /** @description UUIDv7 */
        'x-correlation-id'?: string;
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          contactId: string;
          dateOfBirth?: string | null;
          email: string;
          firstName: string;
          lastName: string;
          mailingAddress?: {
            street: string;
            postcode: string;
            city: string;
            town?: string | null;
            county?: string | null;
            country?: string | null;
          } | null;
          mobilePhone: string;
          phone?: string | null;
          companyRole: 'Owner' | 'AdminContact' | 'Director' | 'Employee';
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            requestType:
              | 'CompanyName'
              | 'MemberEmail'
              | 'MemberPhone'
              | 'MemberTradingAddress'
              | 'MemberAdminAddress'
              | 'AddPerson'
              | 'UpdatePerson'
              | 'RemovePerson';
            correlationId?: string;
          };
        };
      };
    };
  };
  updateMemberDeletePerson: {
    parameters: {
      query?: never;
      header: {
        /** @description UUIDv7 */
        'x-correlation-id'?: string;
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          contactId: string;
          firstName: string;
          lastName: string;
          email: string;
          companyRole: 'Owner' | 'AdminContact' | 'Director' | 'Employee';
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            requestType:
              | 'CompanyName'
              | 'MemberEmail'
              | 'MemberPhone'
              | 'MemberTradingAddress'
              | 'MemberAdminAddress'
              | 'AddPerson'
              | 'UpdatePerson'
              | 'RemovePerson';
            correlationId?: string;
          };
        };
      };
      /** @description Default Response */
      403: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  getServiceVersion: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        serviceId: string;
        versionId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** Format: uuid */
            id: string;
            categoryId: number;
            categoryName: string;
            /** Format: uuid */
            serviceId: string;
            companyId: number;
            name: string;
            description: string;
            homeownerNotes?: string;
            whatIsIncluded: string[];
            whatIsNotIncluded: string[];
            status:
              | 'DRAFT'
              | 'IN_REVIEW'
              | 'PUBLISHED_ACTIVE'
              | 'PUBLISHED_INACTIVE'
              | 'REJECTED'
              | 'ARCHIVED';
          } & {
            days: {
              /** Format: uuid */
              serviceVersionId: string;
              dayOfTheWeek:
                | 'MONDAY'
                | 'TUESDAY'
                | 'WEDNESDAY'
                | 'THURSDAY'
                | 'FRIDAY'
                | 'SATURDAY'
                | 'SUNDAY';
              /** Format: time */
              startTime: string;
              /** Format: time */
              endTime: string;
            }[];
            lineItems: {
              /** Format: uuid */
              serviceVersionId: string;
              priceInPence: number;
              quantifier:
                | 'PER_JOB'
                | 'PER_HOUR'
                | 'PER_DAY'
                | 'PER_ROOM'
                | 'PER_SQUARE_METRE'
                | 'PER_CUBIC_METRE'
                | 'PER_LINEAR_METRE'
                | 'PER_BRICK_BLOCK'
                | 'PER_FIXTURE'
                | 'PER_ITEM'
                | 'PER_TREE'
                | 'PER_VISIT'
                | 'PER_APPLIANCE'
                | 'OTHER';
              notes: string;
            }[];
          };
        };
      };
    };
  };
  getServices: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            pagination: {
              total?: number;
              pageCount?: number;
              page: number;
              size: number;
              hasNext?: boolean;
              hasPrevious?: boolean;
            };
            data: ({
              /** Format: uuid */
              id: string;
              categoryId: number;
              categoryName: string;
              /** Format: uuid */
              serviceId: string;
              companyId: number;
              name: string;
              description: string;
              homeownerNotes?: string;
              whatIsIncluded: string[];
              whatIsNotIncluded: string[];
              status:
                | 'DRAFT'
                | 'IN_REVIEW'
                | 'PUBLISHED_ACTIVE'
                | 'PUBLISHED_INACTIVE'
                | 'REJECTED'
                | 'ARCHIVED';
            } & {
              days: {
                /** Format: uuid */
                serviceVersionId: string;
                dayOfTheWeek:
                  | 'MONDAY'
                  | 'TUESDAY'
                  | 'WEDNESDAY'
                  | 'THURSDAY'
                  | 'FRIDAY'
                  | 'SATURDAY'
                  | 'SUNDAY';
                /** Format: time */
                startTime: string;
                /** Format: time */
                endTime: string;
              }[];
              lineItems: {
                /** Format: uuid */
                serviceVersionId: string;
                priceInPence: number;
                quantifier:
                  | 'PER_JOB'
                  | 'PER_HOUR'
                  | 'PER_DAY'
                  | 'PER_ROOM'
                  | 'PER_SQUARE_METRE'
                  | 'PER_CUBIC_METRE'
                  | 'PER_LINEAR_METRE'
                  | 'PER_BRICK_BLOCK'
                  | 'PER_FIXTURE'
                  | 'PER_ITEM'
                  | 'PER_TREE'
                  | 'PER_VISIT'
                  | 'PER_APPLIANCE'
                  | 'OTHER';
                notes: string;
              }[];
            })[];
          };
        };
      };
    };
  };
  postService: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          categoryId: number;
          name: string;
          description: string;
          days?: {
            day:
              | 'MONDAY'
              | 'TUESDAY'
              | 'WEDNESDAY'
              | 'THURSDAY'
              | 'FRIDAY'
              | 'SATURDAY'
              | 'SUNDAY';
            /** Format: time */
            startTime: string;
            /** Format: time */
            endTime: string;
          }[];
          whatIsIncluded: string[];
          whatIsNotIncluded: string[];
          homeownerNotes?: string;
          priceInPence: number;
          quantifier:
            | 'PER_JOB'
            | 'PER_HOUR'
            | 'PER_DAY'
            | 'PER_ROOM'
            | 'PER_SQUARE_METRE'
            | 'PER_CUBIC_METRE'
            | 'PER_LINEAR_METRE'
            | 'PER_BRICK_BLOCK'
            | 'PER_FIXTURE'
            | 'PER_ITEM'
            | 'PER_TREE'
            | 'PER_VISIT'
            | 'PER_APPLIANCE'
            | 'OTHER';
          homeownerPriceRelatedNotes: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** Format: uuid */
            id: string;
            /** Format: uuid */
            serviceId: string;
            categoryId: number;
            name: string;
            description: string;
            companyId: number;
            homeownerNotes?: string;
            whatIsIncluded: string[];
            whatIsNotIncluded: string[];
            status:
              | 'DRAFT'
              | 'IN_REVIEW'
              | 'PUBLISHED_ACTIVE'
              | 'PUBLISHED_INACTIVE'
              | 'REJECTED'
              | 'ARCHIVED';
            lineItems: {
              priceInPence: number;
              unit:
                | 'PER_JOB'
                | 'PER_HOUR'
                | 'PER_DAY'
                | 'PER_ROOM'
                | 'PER_SQUARE_METRE'
                | 'PER_CUBIC_METRE'
                | 'PER_LINEAR_METRE'
                | 'PER_BRICK_BLOCK'
                | 'PER_FIXTURE'
                | 'PER_ITEM'
                | 'PER_TREE'
                | 'PER_VISIT'
                | 'PER_APPLIANCE'
                | 'OTHER';
              homeownerPriceRelatedNotes: string;
            }[];
            days?: {
              day:
                | 'MONDAY'
                | 'TUESDAY'
                | 'WEDNESDAY'
                | 'THURSDAY'
                | 'FRIDAY'
                | 'SATURDAY'
                | 'SUNDAY';
              /** Format: time */
              startTime: string;
              /** Format: time */
              endTime: string;
            }[];
          };
        };
      };
    };
  };
  postServiceVersion: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        serviceId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          categoryId: number;
          name: string;
          description: string;
          days?: {
            day:
              | 'MONDAY'
              | 'TUESDAY'
              | 'WEDNESDAY'
              | 'THURSDAY'
              | 'FRIDAY'
              | 'SATURDAY'
              | 'SUNDAY';
            /** Format: time */
            startTime: string;
            /** Format: time */
            endTime: string;
          }[];
          whatIsIncluded: string[];
          whatIsNotIncluded: string[];
          homeownerNotes?: string;
          priceInPence: number;
          quantifier:
            | 'PER_JOB'
            | 'PER_HOUR'
            | 'PER_DAY'
            | 'PER_ROOM'
            | 'PER_SQUARE_METRE'
            | 'PER_CUBIC_METRE'
            | 'PER_LINEAR_METRE'
            | 'PER_BRICK_BLOCK'
            | 'PER_FIXTURE'
            | 'PER_ITEM'
            | 'PER_TREE'
            | 'PER_VISIT'
            | 'PER_APPLIANCE'
            | 'OTHER';
          homeownerPriceRelatedNotes: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** Format: uuid */
            id: string;
            /** Format: uuid */
            serviceId: string;
            categoryId: number;
            name: string;
            description: string;
            companyId: number;
            homeownerNotes?: string;
            whatIsIncluded: string[];
            whatIsNotIncluded: string[];
            status:
              | 'DRAFT'
              | 'IN_REVIEW'
              | 'PUBLISHED_ACTIVE'
              | 'PUBLISHED_INACTIVE'
              | 'REJECTED'
              | 'ARCHIVED';
            lineItems: {
              priceInPence: number;
              unit:
                | 'PER_JOB'
                | 'PER_HOUR'
                | 'PER_DAY'
                | 'PER_ROOM'
                | 'PER_SQUARE_METRE'
                | 'PER_CUBIC_METRE'
                | 'PER_LINEAR_METRE'
                | 'PER_BRICK_BLOCK'
                | 'PER_FIXTURE'
                | 'PER_ITEM'
                | 'PER_TREE'
                | 'PER_VISIT'
                | 'PER_APPLIANCE'
                | 'OTHER';
              homeownerPriceRelatedNotes: string;
            }[];
            days?: {
              day:
                | 'MONDAY'
                | 'TUESDAY'
                | 'WEDNESDAY'
                | 'THURSDAY'
                | 'FRIDAY'
                | 'SATURDAY'
                | 'SUNDAY';
              /** Format: time */
              startTime: string;
              /** Format: time */
              endTime: string;
            }[];
          };
        };
      };
    };
  };
  patchServiceVersionStatus: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        serviceId: string;
        versionId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          status:
            | 'DRAFT'
            | 'IN_REVIEW'
            | 'PUBLISHED_ACTIVE'
            | 'PUBLISHED_INACTIVE'
            | 'REJECTED'
            | 'ARCHIVED';
        };
      };
    };
    responses: {
      /** @description Default Response */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  postEssentialTradeService: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          categoryId: number;
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            serviceId: string;
            categoryId: number;
            name: string;
            companyId: number;
          };
        };
      };
    };
  };
  deleteEssentialTradeService: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        serviceId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  patchEssentialTradeService: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        serviceId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          categoryId: number;
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description Identifier in the UUIDv7 format (time-sortable). Read more https://uuid7.com
             */
            serviceId: string;
            categoryId: number;
            name: string;
            companyId: number;
          };
        };
      };
    };
  };
  getMemberInfo: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            data: {
              memberId?: string | null;
              companyId: number;
              name?: string | null;
              isMembershipFlexible: boolean;
              joinedDate?: string | null;
              legacyProductType?: string | null;
              membershipType?: string | null;
              traderId?: number | null;
              uniqueName?: string | null;
              accountBalance: number;
              hasAcceptedEssentialsTerms: boolean;
              isAccountOwner: boolean;
            };
          };
        };
      };
      /** @description Default Response */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  getMemberDetails: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            accountEmail?: string;
            accountPhone?: string;
            isAccountOwner: boolean;
            contacts?: {
              contactId?: string | null;
              dateOfBirth?: string | null;
              email?: string | null;
              firstName?: string | null;
              lastName?: string | null;
              mailingAddress?: {
                city?: string | null;
                country?: string | null;
                county?: string | null;
                postcode?: string | null;
                street?: string | null;
                town?: string | null;
              } | null;
              mobilePhone?: string | null;
              phone?: string | null;
              roleId?: number | null;
              role?:
                | (
                    | '1'
                    | '2'
                    | '3'
                    | '4'
                    | 'Owner'
                    | 'AdminContact'
                    | 'Director'
                    | 'Employee'
                  )
                | null;
            }[];
            companyAdminAddress?: {
              city?: string | null;
              country?: string | null;
              county?: string | null;
              postcode?: string | null;
              street?: string | null;
              town?: string | null;
            };
            companyPrimaryPostalAddress?: {
              city?: string | null;
              country?: string | null;
              county?: string | null;
              postcode?: string | null;
              street?: string | null;
              town?: string | null;
            };
          };
        };
      };
      /** @description Default Response */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  getRevettingStatus: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            revettingStatus: 'REQUIRED' | 'NOT_REQUIRED';
          };
        };
      };
      /** @description Default Response */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      422: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      500: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  getRevettingUrl: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            url: string;
          };
        };
      };
      /** @description Default Response */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      422: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      500: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  createCampaigns: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        /** @description Company ID for which to create campaigns */
        companyId: number;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          /** @enum {string} */
          type?: 'MdpSponsoredSearch';
          maxBudget: number;
          pausedUntil: string | null;
          /** Format: date */
          budgetPeriod?: string;
          category: {
            categoryId: number;
            isSearchable: boolean;
            name?: string;
          };
          subCategories: {
            categoryId: number;
            parentCategoryId: number;
            name?: string;
          }[];
          geographies: {
            value: string;
            type:
              | 'PostcodeArea'
              | 'PostcodeDistrict'
              | 'PostcodeSector'
              | 'DirectoryArea';
          }[];
          mdpSponsoredSearch: {
            endDate?: string | null;
            bidAmount?: number | null;
            primaryCampaignId: string;
            searchType: 'Listings' | 'RequestAQuote';
            bidStrategy?: 'player' | 'leader' | 'competitor' | 'none';
          };
        }[];
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': string[];
        };
      };
      /** @description Default Response */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      401: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      403: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      500: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  getCampaignStats: {
    parameters: {
      query: {
        from: string;
        to: string;
        dimensions?:
          | ('companyId' | 'campaignId' | 'searchType')[]
          | ('companyId' | 'campaignId' | 'searchType');
        companyId: number;
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            impressions: number;
            clicks: number;
            cpc: number;
            clickThroughRate: number;
            spend: number;
          }[];
        };
      };
      /** @description Default Response */
      401: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      403: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  getExperiences: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            experiences: {
              experienceId: number;
              currencyType: number;
              name: string;
              minBid: number;
              maxBid: number;
              bidStrategyPlayer: number;
              bidStrategyLeader: number;
              bidStrategyCompetitor: number;
            }[];
          };
        };
      };
      /** @description Default Response */
      401: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      403: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  postProject: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          /** Format: uuid */
          jobId: string;
          projectName: string;
          /** Format: date */
          startDate: string;
          /** Format: date */
          endDate: string;
          cost: number;
          /** @enum {unknown} */
          status?: 'DRAFT' | 'ACTIVE';
          albumId: string | null;
          reviewId: string | null;
        };
      };
    };
    responses: {
      /** @description Default Response */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** Format: uuid */
            id: string;
            projectName: string;
            /** Format: uuid */
            jobId: string;
            /** Format: date */
            startDate: string;
            /** Format: date */
            endDate: string;
            cost: number;
            /** @enum {unknown} */
            status: 'DRAFT' | 'ACTIVE';
            album:
              | ({
                  description?: string | null;
                  featuredProjectJobId?: string | null;
                  id: string;
                  items: {
                    id: string;
                    /** @enum {unknown} */
                    status:
                      | 'SUBMITTED'
                      | 'LIVE'
                      | 'AI_FLAGGED'
                      | 'HUMAN_FLAGGED'
                      | 'UPLOAD_FAILED';
                    /** @enum {unknown} */
                    type: 'PHOTO' | 'VIDEO';
                    url?: string | null;
                    description?: string | null;
                    imageId?: string | null;
                    label?: ('NONE' | 'BEFORE' | 'DURING' | 'AFTER') | null;
                    metadata?: {
                      width?: number | null;
                      height?: number | null;
                      rotation?: null | 0 | 90 | 180 | 270;
                    } | null;
                    migrationId?: string | null;
                    title?: string | null;
                    youTubeId?: string | null;
                  }[];
                  itemsOrder: string[];
                  legacyAlbumId?: number | null;
                  title: string;
                  thumbnail?: string;
                  url?: string;
                } | null)
              | null;
            review: {
              companyId: number;
              title: string;
              review: string;
              jobId?: string;
              consumerId?: string;
              /** @enum {unknown} */
              verified?: 'VERIFIED' | 'PENDING' | 'FAILED' | 'NOT_VERIFIED';
              /** @enum {unknown} */
              type: 'WORK_NOT_CARRIED' | 'WORK_CARRIED';
              categoryId?: number;
              topLevelCategoryId?: number;
              rating?: {
                qualityOfWorkmanship?: number;
                tidiness?: number;
                reliabilityAndTimekeeping?: number;
                courtesy?: number;
                communication?: number;
                rating?: number;
              };
              /** @enum {unknown} */
              priceAsQuoted?:
                | 'AS_ORIGINALLY_QUOTED'
                | 'MORE_THAN_ORIGINALLY_QUOTED'
                | 'LESS_THAN_ORIGINALLY_QUOTED'
                | 'DID_NOT_GET_A_QUOTE'
                | 'REQUESTED_ADDITIONAL_WORK';
              valueOfWork?: number;
              id: string;
              reply?: {
                id: string;
                reply: string;
                /** @enum {unknown} */
                status: 'NOT_PUBLISHED' | 'PUBLISHED' | 'PENDING';
                createdAt: string;
                updatedAt: string;
              };
              /** @enum {unknown} */
              status:
                | 'NOT_PUBLISHED'
                | 'PUBLISHED'
                | 'SCHEDULED'
                | 'DO_NOT_PUBLISH'
                | 'PENDING'
                | 'PARKED'
                | 'ARCHIVED';
              createdAt: string;
              updatedAt: string;
              legacyId?: string;
              location?: {
                postcode?: string;
              };
              isComplaint?: boolean;
              notRecommendReason?: string;
              isReported?: boolean;
              publishedAt?: string;
              reviewer?: {
                firstName: string;
                lastName: string;
                postcode?: string;
              };
            } | null;
          };
        };
      };
    };
  };
  getProject: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        projectId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** Format: uuid */
            id: string;
            projectName: string;
            /** Format: uuid */
            jobId: string;
            /** Format: date */
            startDate: string;
            /** Format: date */
            endDate: string;
            cost: number;
            /** @enum {unknown} */
            status: 'DRAFT' | 'ACTIVE';
            album:
              | ({
                  description?: string | null;
                  featuredProjectJobId?: string | null;
                  id: string;
                  items: {
                    id: string;
                    /** @enum {unknown} */
                    status:
                      | 'SUBMITTED'
                      | 'LIVE'
                      | 'AI_FLAGGED'
                      | 'HUMAN_FLAGGED'
                      | 'UPLOAD_FAILED';
                    /** @enum {unknown} */
                    type: 'PHOTO' | 'VIDEO';
                    url?: string | null;
                    description?: string | null;
                    imageId?: string | null;
                    label?: ('NONE' | 'BEFORE' | 'DURING' | 'AFTER') | null;
                    metadata?: {
                      width?: number | null;
                      height?: number | null;
                      rotation?: null | 0 | 90 | 180 | 270;
                    } | null;
                    migrationId?: string | null;
                    title?: string | null;
                    youTubeId?: string | null;
                  }[];
                  itemsOrder: string[];
                  legacyAlbumId?: number | null;
                  title: string;
                  thumbnail?: string;
                  url?: string;
                } | null)
              | null;
            review: {
              companyId: number;
              title: string;
              review: string;
              jobId?: string;
              consumerId?: string;
              /** @enum {unknown} */
              verified?: 'VERIFIED' | 'PENDING' | 'FAILED' | 'NOT_VERIFIED';
              /** @enum {unknown} */
              type: 'WORK_NOT_CARRIED' | 'WORK_CARRIED';
              categoryId?: number;
              topLevelCategoryId?: number;
              rating?: {
                qualityOfWorkmanship?: number;
                tidiness?: number;
                reliabilityAndTimekeeping?: number;
                courtesy?: number;
                communication?: number;
                rating?: number;
              };
              /** @enum {unknown} */
              priceAsQuoted?:
                | 'AS_ORIGINALLY_QUOTED'
                | 'MORE_THAN_ORIGINALLY_QUOTED'
                | 'LESS_THAN_ORIGINALLY_QUOTED'
                | 'DID_NOT_GET_A_QUOTE'
                | 'REQUESTED_ADDITIONAL_WORK';
              valueOfWork?: number;
              id: string;
              reply?: {
                id: string;
                reply: string;
                /** @enum {unknown} */
                status: 'NOT_PUBLISHED' | 'PUBLISHED' | 'PENDING';
                createdAt: string;
                updatedAt: string;
              };
              /** @enum {unknown} */
              status:
                | 'NOT_PUBLISHED'
                | 'PUBLISHED'
                | 'SCHEDULED'
                | 'DO_NOT_PUBLISH'
                | 'PENDING'
                | 'PARKED'
                | 'ARCHIVED';
              createdAt: string;
              updatedAt: string;
              legacyId?: string;
              location?: {
                postcode?: string;
              };
              isComplaint?: boolean;
              notRecommendReason?: string;
              isReported?: boolean;
              publishedAt?: string;
              reviewer?: {
                firstName: string;
                lastName: string;
                postcode?: string;
              };
            } | null;
          };
        };
      };
    };
  };
  deleteProject: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        projectId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No Content */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  updateProject: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        projectId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          /** Format: uuid */
          jobId?: string;
          projectName?: string;
          /** Format: date */
          startDate?: string;
          /** Format: date */
          endDate?: string;
          cost?: number;
          /** @enum {unknown} */
          status?: 'DRAFT' | 'ACTIVE';
          albumId?: string | null;
          reviewId?: string | null;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** Format: uuid */
            id: string;
            projectName: string;
            /** Format: uuid */
            jobId: string;
            /** Format: date */
            startDate: string;
            /** Format: date */
            endDate: string;
            cost: number;
            /** @enum {unknown} */
            status: 'DRAFT' | 'ACTIVE';
            album:
              | ({
                  description?: string | null;
                  featuredProjectJobId?: string | null;
                  id: string;
                  items: {
                    id: string;
                    /** @enum {unknown} */
                    status:
                      | 'SUBMITTED'
                      | 'LIVE'
                      | 'AI_FLAGGED'
                      | 'HUMAN_FLAGGED'
                      | 'UPLOAD_FAILED';
                    /** @enum {unknown} */
                    type: 'PHOTO' | 'VIDEO';
                    url?: string | null;
                    description?: string | null;
                    imageId?: string | null;
                    label?: ('NONE' | 'BEFORE' | 'DURING' | 'AFTER') | null;
                    metadata?: {
                      width?: number | null;
                      height?: number | null;
                      rotation?: null | 0 | 90 | 180 | 270;
                    } | null;
                    migrationId?: string | null;
                    title?: string | null;
                    youTubeId?: string | null;
                  }[];
                  itemsOrder: string[];
                  legacyAlbumId?: number | null;
                  title: string;
                  thumbnail?: string;
                  url?: string;
                } | null)
              | null;
            review: {
              companyId: number;
              title: string;
              review: string;
              jobId?: string;
              consumerId?: string;
              /** @enum {unknown} */
              verified?: 'VERIFIED' | 'PENDING' | 'FAILED' | 'NOT_VERIFIED';
              /** @enum {unknown} */
              type: 'WORK_NOT_CARRIED' | 'WORK_CARRIED';
              categoryId?: number;
              topLevelCategoryId?: number;
              rating?: {
                qualityOfWorkmanship?: number;
                tidiness?: number;
                reliabilityAndTimekeeping?: number;
                courtesy?: number;
                communication?: number;
                rating?: number;
              };
              /** @enum {unknown} */
              priceAsQuoted?:
                | 'AS_ORIGINALLY_QUOTED'
                | 'MORE_THAN_ORIGINALLY_QUOTED'
                | 'LESS_THAN_ORIGINALLY_QUOTED'
                | 'DID_NOT_GET_A_QUOTE'
                | 'REQUESTED_ADDITIONAL_WORK';
              valueOfWork?: number;
              id: string;
              reply?: {
                id: string;
                reply: string;
                /** @enum {unknown} */
                status: 'NOT_PUBLISHED' | 'PUBLISHED' | 'PENDING';
                createdAt: string;
                updatedAt: string;
              };
              /** @enum {unknown} */
              status:
                | 'NOT_PUBLISHED'
                | 'PUBLISHED'
                | 'SCHEDULED'
                | 'DO_NOT_PUBLISH'
                | 'PENDING'
                | 'PARKED'
                | 'ARCHIVED';
              createdAt: string;
              updatedAt: string;
              legacyId?: string;
              location?: {
                postcode?: string;
              };
              isComplaint?: boolean;
              notRecommendReason?: string;
              isReported?: boolean;
              publishedAt?: string;
              reviewer?: {
                firstName: string;
                lastName: string;
                postcode?: string;
              };
            } | null;
          };
        };
      };
    };
  };
  listCompanyProjects: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            data: {
              /** Format: uuid */
              id: string;
              projectName: string;
              /** Format: uuid */
              jobId: string;
              /** Format: date */
              startDate: string;
              /** Format: date */
              endDate: string;
              cost: number;
              /** @enum {unknown} */
              status: 'DRAFT' | 'ACTIVE';
              album:
                | ({
                    description?: string | null;
                    featuredProjectJobId?: string | null;
                    id: string;
                    items: {
                      id: string;
                      /** @enum {unknown} */
                      status:
                        | 'SUBMITTED'
                        | 'LIVE'
                        | 'AI_FLAGGED'
                        | 'HUMAN_FLAGGED'
                        | 'UPLOAD_FAILED';
                      /** @enum {unknown} */
                      type: 'PHOTO' | 'VIDEO';
                      url?: string | null;
                      description?: string | null;
                      imageId?: string | null;
                      label?: ('NONE' | 'BEFORE' | 'DURING' | 'AFTER') | null;
                      metadata?: {
                        width?: number | null;
                        height?: number | null;
                        rotation?: null | 0 | 90 | 180 | 270;
                      } | null;
                      migrationId?: string | null;
                      title?: string | null;
                      youTubeId?: string | null;
                    }[];
                    itemsOrder: string[];
                    legacyAlbumId?: number | null;
                    title: string;
                    thumbnail?: string;
                    url?: string;
                  } | null)
                | null;
              review: {
                companyId: number;
                title: string;
                review: string;
                jobId?: string;
                consumerId?: string;
                /** @enum {unknown} */
                verified?: 'VERIFIED' | 'PENDING' | 'FAILED' | 'NOT_VERIFIED';
                /** @enum {unknown} */
                type: 'WORK_NOT_CARRIED' | 'WORK_CARRIED';
                categoryId?: number;
                topLevelCategoryId?: number;
                rating?: {
                  qualityOfWorkmanship?: number;
                  tidiness?: number;
                  reliabilityAndTimekeeping?: number;
                  courtesy?: number;
                  communication?: number;
                  rating?: number;
                };
                /** @enum {unknown} */
                priceAsQuoted?:
                  | 'AS_ORIGINALLY_QUOTED'
                  | 'MORE_THAN_ORIGINALLY_QUOTED'
                  | 'LESS_THAN_ORIGINALLY_QUOTED'
                  | 'DID_NOT_GET_A_QUOTE'
                  | 'REQUESTED_ADDITIONAL_WORK';
                valueOfWork?: number;
                id: string;
                reply?: {
                  id: string;
                  reply: string;
                  /** @enum {unknown} */
                  status: 'NOT_PUBLISHED' | 'PUBLISHED' | 'PENDING';
                  createdAt: string;
                  updatedAt: string;
                };
                /** @enum {unknown} */
                status:
                  | 'NOT_PUBLISHED'
                  | 'PUBLISHED'
                  | 'SCHEDULED'
                  | 'DO_NOT_PUBLISH'
                  | 'PENDING'
                  | 'PARKED'
                  | 'ARCHIVED';
                createdAt: string;
                updatedAt: string;
                legacyId?: string;
                location?: {
                  postcode?: string;
                };
                isComplaint?: boolean;
                notRecommendReason?: string;
                isReported?: boolean;
                publishedAt?: string;
                reviewer?: {
                  firstName: string;
                  lastName: string;
                  postcode?: string;
                };
              } | null;
            }[];
            page: number;
            size: number;
            total: number;
          };
        };
      };
    };
  };
  projectCompletedJobs: {
    parameters: {
      query?: {
        page?: number;
        size?: number;
      };
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            page: number;
            size: number;
            total: number;
            data: {
              /** Format: uuid */
              id: string;
              title: string;
              category: string;
              date: string;
              isAssignedToProject: boolean;
            }[];
          };
        };
      };
    };
  };
  gettradeinsights: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            companyId: number | null;
            engagement: {
              engagementThirtyDays: {
                respondedPercent: number | null;
                unansweredPercent: number | null;
                acceptedPercent: number | null;
                declinedPercent: number | null;
              } | null;
            } | null;
            opportunities: {
              sevenDays: {
                leads: number | null;
                leadsVariancePercent: number | null;
                accepted: number | null;
                acceptedVariancePercent: number | null;
                jobs: number | null;
                jobsVariancePercent: number | null;
              } | null;
              fourteenDays: {
                leads: number | null;
                leadsVariancePercent: number | null;
                accepted: number | null;
                acceptedVariancePercent: number | null;
                jobs: number | null;
                jobsVariancePercent: number | null;
              } | null;
              thirtyDays: {
                leads: number | null;
                leadsVariancePercent: number | null;
                accepted: number | null;
                acceptedVariancePercent: number | null;
                jobs: number | null;
                jobsVariancePercent: number | null;
              } | null;
              ninetyDays: {
                leads: number | null;
                leadsVariancePercent: number | null;
                accepted: number | null;
                acceptedVariancePercent: number | null;
                jobs: number | null;
                jobsVariancePercent: number | null;
              } | null;
              oneYear: {
                leads: number | null;
                leadsVariancePercent: number | null;
                accepted: number | null;
                acceptedVariancePercent: number | null;
                jobs: number | null;
                jobsVariancePercent: number | null;
              } | null;
            } | null;
            reviews: {
              reviewsThirtyDays: {
                pastYearAverageScore: number | null;
                pastMonthsAverageScore: number | null;
                pastMonthPublishedReviews: number | null;
                averageReviewScoreChangePercent: number | null;
              } | null;
            } | null;
            searches: {
              searchesThirtyDays: {
                company: {
                  searchVisibilityPercent: number | null;
                  searchPositionChangePercent: number | null;
                  profileViewChangePercent: number | null;
                } | null;
                categories:
                  | {
                      categoryId: number | null;
                      categoryName: string | null;
                      searchVisibilityPercent: number | null;
                      searchPositionChangePercent: number | null;
                      profileViewChangePercent: number | null;
                    }[]
                  | null;
              } | null;
            } | null;
          };
        };
      };
      /** @description Default Response */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      422: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
      /** @description Default Response */
      500: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            status: number;
            title: string;
            /** Format: uri-reference */
            type: string;
            /** Format: uri */
            instance: string;
            detail: string;
          };
        };
      };
    };
  };
  getSecureContactsOptions: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            mobileOptions: {
              id: string;
              name: string;
              number: string;
            }[];
          };
        };
      };
    };
  };
  getAssignedSecureContacts: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            secureNumber: string;
            destinationNumber: string;
          };
        };
      };
    };
  };
  patchAssignedSecureContact: {
    parameters: {
      query?: never;
      header: {
        'x-trade-company-id': number;
      };
      path: {
        secureContactId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': {
          contactId: string;
        };
      };
    };
    responses: {
      /** @description Default Response */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            id: string;
            name?: string;
            number?: string;
            /** @enum {unknown} */
            type?: 'MOBILE' | 'LANDLINE' | 'DIRECTORY';
            areaCode?: string;
            contactId?: string;
            divert?: {
              locked?: boolean;
              secureNumber?: string;
              displaySecureNumber?: string;
              destinationNumber?: string;
              ntsNumber?: string;
              /** @enum {unknown} */
              syncStatus?: 'SCHEDULED' | 'PENDING' | 'SYNCED';
            };
            tag?: string;
          };
        };
      };
    };
  };
}
