import { cleanup, renderHook, waitFor } from '@testing-library/react-native';
import { financeTradeBffApi } from 'src/data/api/trade-app-bff/finance';
import { InternalAxiosRequestConfig } from 'axios';
import { createQueryClientWrapper } from 'src/utilities/tanstack-query/tanstack-test-utils';
import { useZuoraStatus } from './useZuoraStatus';

const mockCompanyId = 123456;

const financeSpy = jest.spyOn(financeTradeBffApi, 'getZuoraStatus');

jest.mock('src/hooks/useUser', () => ({
  useUserContext: () => ({
    companyId: mockCompanyId,
  }),
}));

jest.mock('src/hooks/useUserAccessLevels', () => {
  const MockUserAccessLevel = {
    Essentials: 'Essentials',
  };

  return {
    UserAccessLevel: MockUserAccessLevel,
    useUserAccessLevels: jest.fn(() => MockUserAccessLevel.Essentials),
  };
});

const wrapper = createQueryClientWrapper();

describe('Hooks | useZuoraStatus', () => {
  afterEach(() => {
    jest.clearAllMocks();
    cleanup();
  });

  beforeEach(() => {
    financeSpy.mockResolvedValue({
      data: {
        defaultPaymentMethodState: 'active',
      },
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as InternalAxiosRequestConfig,
    });
  });

  it('renders data correctly when status is active', async () => {
    const { result } = renderHook(() => useZuoraStatus(), {
      wrapper,
    });

    await waitFor(() => {
      expect(result.current.data).toEqual({
        defaultPaymentMethodState: 'active',
      });
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isPaymentStatusActive).toBe(true);
      expect(financeSpy).toHaveBeenCalledWith(mockCompanyId);
    });
  });

  it('renders data correctly when status is a status that is not active', async () => {
    financeSpy.mockResolvedValue({
      data: {
        defaultPaymentMethodState: 'closed',
      },
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as InternalAxiosRequestConfig,
    });

    const { result } = renderHook(() => useZuoraStatus(), {
      wrapper,
    });

    await waitFor(() => {
      expect(result.current.data).toEqual({
        defaultPaymentMethodState: 'closed',
      });
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isPaymentStatusActive).toBe(false);
      expect(financeSpy).toHaveBeenCalledWith(mockCompanyId);
    });
  });
});
