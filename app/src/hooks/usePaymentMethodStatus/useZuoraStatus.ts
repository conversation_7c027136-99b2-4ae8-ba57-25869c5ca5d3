import { useQuery } from '@tanstack/react-query';
import { financeTradeBffApi } from 'src/data/api/trade-app-bff/finance';
import { ZuoraStatus } from 'src/data/schemas/api/trade-app-bff/finance/PaymentMethod';
import { useMemo } from 'react';
import { useUserContext } from '../useUser';
import { UserAccessLevel, useUserAccessLevels } from '../useUserAccessLevels';
import { QUERY_KEY_GET_ZUORA_STATUS } from './constants';

const validateCompanyId = (companyId: number | undefined): number => {
  if (!companyId) {
    throw new Error(
      'Unable to retrieve payment method status, there was a problem retrieving the companyId',
    );
  }

  return companyId;
};

type HookReturn = {
  data: ZuoraStatus | undefined;
  isLoading: boolean;
  error: Error | null;
  isPaymentStatusActive: boolean;
};

const ONE_HOUR_IN_MS = 1000 * 60 * 60;

/**
 * Makes call to `/zuora/status` endpoint to retrieve the payment method status.
 * Also provides a lightweight bool value to indicate if the default payment method is active.
 */
export const useZuoraStatus = (): HookReturn => {
  const { companyId } = useUserContext();
  const userAccessLevel = useUserAccessLevels();

  const { data, isLoading, error } = useQuery({
    queryKey: [QUERY_KEY_GET_ZUORA_STATUS, companyId],
    queryFn: () =>
      financeTradeBffApi
        .getZuoraStatus(validateCompanyId(companyId))
        .then((response) => response.data),
    enabled: userAccessLevel === UserAccessLevel.Essentials,
    networkMode: 'offlineFirst',
    staleTime: ONE_HOUR_IN_MS,
    gcTime: Infinity,
  });

  const isPaymentStatusActive = useMemo(
    () => data?.defaultPaymentMethodState === 'active',
    [data?.defaultPaymentMethodState],
  );

  return {
    data,
    isLoading,
    error,
    isPaymentStatusActive,
  };
};
