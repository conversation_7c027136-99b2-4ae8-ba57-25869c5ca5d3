import { cleanup, renderHook } from '@testing-library/react-native';
import { useNavigationLinks } from 'src/context/NavigationLinksContext';
import { useReviews } from 'src/screens/Reviews/hooks/useReviews';
import { useHasRecentReviews } from 'src/hooks/useHasRecentReviews';
import { TradeReplyStatus } from 'src/data/schemas/firestore/reviews';
import { UserAccessLevel } from 'src/hooks/useUserAccessLevels';
import { calculateProgress } from '../utils/calculateProgress';
import { useReviewsProgress } from './useReviewsProgress';

jest.mock('src/context/NavigationLinksContext', () => ({
  useNavigationLinks: jest.fn(),
}));

jest.mock('src/screens/Reviews/hooks/useReviews', () => ({
  useReviews: jest.fn(),
}));

jest.mock('src/hooks/useHasRecentReviews', () => ({
  useHasRecentReviews: jest.fn(),
}));

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({ navigate: jest.fn() }),
}));

const reviewPublished = {
  id: '1',
  datePublished: new Date().toISOString(),
  tradeReplyStatus: TradeReplyStatus.PUBLISHED,
};

const reviewNotPublished = {
  id: '2',
  datePublished: new Date().toISOString(),
  tradeReplyStatus: 'DRAFT',
};

describe('useReviewsProgress', () => {
  afterEach(cleanup);

  describe("when user access level is 'Pending'", () => {
    beforeEach(() => {
      (useNavigationLinks as jest.Mock).mockReturnValue({
        userAccessLevel: UserAccessLevel.Pending,
      });
    });

    it.each([[TradeReplyStatus.PUBLISHED], [TradeReplyStatus.PENDING]])(
      'returns true on checklist when either the trade reply status is PUBLISHED or PENDING',
      (tradeReplyStatus) => {
        const mockReview = {
          id: '1',
          datePublished: new Date().toISOString(),
          tradeReplyStatus,
        };
        (useHasRecentReviews as jest.Mock).mockReturnValue(true);
        (useReviews as jest.Mock).mockReturnValue({
          reviewsData: [mockReview],
        });
        const { result } = renderHook(() => useReviewsProgress());

        const itemsWithoutOnPress = result.current.reviewsActionItems.map(
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          ({ onPress, analyticsId, ...rest }) => rest,
        );

        const expectedChecklist = [
          {
            id: 'requestReview',
            label: 'Get at least one published review',
            isAlreadyComplete: true,
          },
          {
            id: 'replyToAReview',
            label: 'Reply to a review',
            isAlreadyComplete: true,
          },
        ];

        expect(result.current.reviewsProgress).toEqual(1);
        expect(itemsWithoutOnPress).toEqual(expectedChecklist);
      },
    );

    it('returns a checklist (with only requestReview and replyToAReview items) and progress 1 when all conditions are met', () => {
      (useReviews as jest.Mock).mockReturnValue({
        reviewsData: [reviewPublished, reviewNotPublished],
      });
      (useHasRecentReviews as jest.Mock).mockReturnValue(true);

      const { result } = renderHook(() => useReviewsProgress());

      const itemsWithoutOnPress = result.current.reviewsActionItems.map(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        ({ onPress, analyticsId, ...rest }) => rest,
      );

      const expectedChecklist = [
        {
          id: 'requestReview',
          label: 'Get at least one published review',
          isAlreadyComplete: true,
        },
        {
          id: 'replyToAReview',
          label: 'Reply to a review',
          isAlreadyComplete: true,
        },
      ];

      expect(itemsWithoutOnPress).toEqual(expectedChecklist);
      expect(result.current.reviewsProgress).toEqual(
        calculateProgress(expectedChecklist),
      );
    });

    it('marks the "requestReview" item incomplete when there are no recent reviews', () => {
      (useReviews as jest.Mock).mockReturnValue({
        reviewsData: [reviewPublished, reviewNotPublished],
      });
      (useHasRecentReviews as jest.Mock).mockReturnValue(false);

      const { result } = renderHook(() => useReviewsProgress());
      const requestReviewItem = result.current.reviewsActionItems.find(
        (item) => item.id === 'requestReview',
      );
      expect(requestReviewItem?.isAlreadyComplete).toBeFalsy();

      const expectedChecklist = [
        {
          id: 'requestReview',
          label: 'Get at least one published review',
          isAlreadyComplete: false,
        },
        {
          id: 'replyToAReview',
          label: 'Reply to a review',
          isAlreadyComplete: true,
        },
      ];
      expect(result.current.reviewsProgress).toEqual(
        calculateProgress(expectedChecklist),
      );
    });
  });

  describe("when user access level is 'ActiveOnboarding'", () => {
    beforeEach(() => {
      (useNavigationLinks as jest.Mock).mockReturnValue({
        userAccessLevel: UserAccessLevel.ActiveOnboarding,
      });
    });

    it('returns a checklist with only requestReview and replyToAReview items and computes correct progress when all conditions are met', () => {
      (useReviews as jest.Mock).mockReturnValue({
        reviewsData: [reviewPublished, reviewNotPublished],
      });
      (useHasRecentReviews as jest.Mock).mockReturnValue(true);

      const { result } = renderHook(() => useReviewsProgress());

      const itemsWithoutOnPress = result.current.reviewsActionItems.map(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        ({ onPress, analyticsId, ...rest }) => rest,
      );
      const expectedChecklist = [
        {
          id: 'requestReview',
          label: 'Get at least one published review',
          isAlreadyComplete: true,
        },
        {
          id: 'replyToAReview',
          label: 'Reply to a review',
          isAlreadyComplete: true,
        },
      ];

      expect(itemsWithoutOnPress).toEqual(expectedChecklist);
      expect(result.current.reviewsProgress).toEqual(
        calculateProgress(expectedChecklist),
      );
    });

    it('marks the "requestReview" item incomplete when there are no recent reviews', () => {
      (useReviews as jest.Mock).mockReturnValue({
        reviewsData: [reviewPublished, reviewNotPublished],
      });
      (useHasRecentReviews as jest.Mock).mockReturnValue(false);

      const { result } = renderHook(() => useReviewsProgress());
      const requestReviewItem = result.current.reviewsActionItems.find(
        (item) => item.id === 'requestReview',
      );
      expect(requestReviewItem?.isAlreadyComplete).toBeFalsy();

      const expectedChecklist = [
        {
          id: 'requestReview',
          label: 'Get at least one published review',
          isAlreadyComplete: false,
        },
        {
          id: 'replyToAReview',
          label: 'Reply to a review',
          isAlreadyComplete: true,
        },
      ];
      expect(result.current.reviewsProgress).toEqual(
        calculateProgress(expectedChecklist),
      );
    });

    it.each([[TradeReplyStatus.PUBLISHED], [TradeReplyStatus.PENDING]])(
      'returns true on checklist when either the trade reply status is PUBLISHED or PENDING',
      (tradeReplyStatus) => {
        const mockReview = {
          id: '1',
          datePublished: new Date().toISOString(),
          tradeReplyStatus,
        };
        (useHasRecentReviews as jest.Mock).mockReturnValue(true);
        (useReviews as jest.Mock).mockReturnValue({
          reviewsData: [mockReview],
        });
        const { result } = renderHook(() => useReviewsProgress());

        const itemsWithoutOnPress = result.current.reviewsActionItems.map(
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          ({ onPress, analyticsId, ...rest }) => rest,
        );

        const expectedChecklist = [
          {
            id: 'requestReview',
            label: 'Get at least one published review',
            isAlreadyComplete: true,
          },
          {
            id: 'replyToAReview',
            label: 'Reply to a review',
            isAlreadyComplete: true,
          },
        ];

        expect(result.current.reviewsProgress).toEqual(1);
        expect(itemsWithoutOnPress).toEqual(expectedChecklist);
      },
    );
  });

  describe("when user access level is 'Essentials'", () => {
    beforeEach(() => {
      (useNavigationLinks as jest.Mock).mockReturnValue({
        userAccessLevel: UserAccessLevel.Essentials,
      });
    });

    it('returns a full checklist (all three items) and progress 1 when all conditions are met', () => {
      (useReviews as jest.Mock).mockReturnValue({
        reviewsData: [reviewPublished, reviewPublished, reviewNotPublished],
      });
      (useHasRecentReviews as jest.Mock).mockReturnValue(true);

      const { result } = renderHook(() => useReviewsProgress());

      const itemsWithoutOnPress = result.current.reviewsActionItems.map(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        ({ onPress, analyticsId, ...rest }) => rest,
      );
      const expectedChecklist = [
        {
          id: 'requestReview',
          label: 'Get at least one published review',
          isAlreadyComplete: true,
        },
        {
          id: 'request2Reviews',
          label: 'Request 2 reviews per month',
          isAlreadyComplete: true,
        },
        {
          id: 'replyToAReview',
          label: 'Reply to a review',
          isAlreadyComplete: true,
        },
      ];

      expect(itemsWithoutOnPress).toEqual(expectedChecklist);
      expect(result.current.reviewsProgress).toEqual(
        calculateProgress(expectedChecklist),
      );
    });

    it('marks the "request2Reviews" item incomplete when fewer than 2 reviews have been requested in the current month', () => {
      (useReviews as jest.Mock).mockReturnValue({
        reviewsData: [reviewPublished],
      });
      (useHasRecentReviews as jest.Mock).mockReturnValue(true);

      const { result } = renderHook(() => useReviewsProgress());
      const request2ReviewsItem = result.current.reviewsActionItems.find(
        (item) => item.id === 'request2Reviews',
      );
      expect(request2ReviewsItem?.isAlreadyComplete).toBeFalsy();

      const expectedChecklist = [
        {
          id: 'requestReview',
          label: 'Get at least one published review',
          isAlreadyComplete: true,
        },
        {
          id: 'request2Reviews',
          label: 'Request 2 reviews per month',
          isAlreadyComplete: false,
        },
        {
          id: 'replyToAReview',
          label: 'Reply to a review',
          isAlreadyComplete: true,
        },
      ];
      expect(result.current.reviewsProgress).toEqual(
        calculateProgress(expectedChecklist),
      );
    });

    it.each([[TradeReplyStatus.PUBLISHED], [TradeReplyStatus.PENDING]])(
      'returns true on checklist when either the trade reply status is PUBLISHED or PENDING',
      (tradeReplyStatus) => {
        const mockReview = {
          id: '1',
          datePublished: new Date().toISOString(),
          tradeReplyStatus,
        };
        (useHasRecentReviews as jest.Mock).mockReturnValue(true);
        (useReviews as jest.Mock).mockReturnValue({
          reviewsData: [mockReview],
        });
        const { result } = renderHook(() => useReviewsProgress());

        const itemsWithoutOnPress = result.current.reviewsActionItems.map(
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          ({ onPress, analyticsId, ...rest }) => rest,
        );

        const expectedChecklist = [
          {
            id: 'requestReview',
            label: 'Get at least one published review',
            isAlreadyComplete: true,
          },
          {
            id: 'request2Reviews',
            label: 'Request 2 reviews per month',
            isAlreadyComplete: false,
          },
          {
            id: 'replyToAReview',
            label: 'Reply to a review',
            isAlreadyComplete: true,
          },
        ];
        expect(result.current.reviewsProgress).toEqual(
          calculateProgress(expectedChecklist),
        );
        expect(itemsWithoutOnPress).toEqual(expectedChecklist);
      },
    );
  });

  describe("when user access level is not 'Pending', 'ActiveOnboarding' or 'Essentials'", () => {
    it('returns an empty checklist and progress 0', () => {
      (useNavigationLinks as jest.Mock).mockReturnValue({
        userAccessLevel: UserAccessLevel.Default,
      });
      (useReviews as jest.Mock).mockReturnValue({
        reviewsData: [reviewPublished],
      });
      (useHasRecentReviews as jest.Mock).mockReturnValue(true);

      const { result } = renderHook(() => useReviewsProgress());
      expect(result.current.reviewsActionItems).toEqual([]);
      expect(result.current.reviewsProgress).toEqual(0);
    });
  });
});
