import { useCompanyContext } from 'src/hooks/useCompany';
import { useUserContext } from 'src/hooks/useUser';
import { useReviews } from '../Reviews/hooks/useReviews';
import { OnboardingActionSuggestionsChecklistItem } from './constants';

export const defaultReviewReturn: ReturnType<typeof useReviews> = {
  reviewsData: [],
  reviewsDataLoading: false,
  reviewsDataLoadingMore: false,
  loadMoreReviews: jest.fn(),
  reviewsDataError: false,
};

export const defaultCompanyContextReturn: Partial<
  ReturnType<typeof useCompanyContext>
> = {
  companyV2: {
    _documentId: 0,
    details: {
      logo: { url: 'https://picsum.photos/200' },
      heroBanner: { url: 'https://picsum.photos/200' },
    },
    services: [{ id: '0', name: 'Cleaning' }],
    albums: [
      {
        id: '0',
        title: 'the album',
        description: "the album's description",
        imageCount: 5,
        flaggedImageCount: 0,
      },
    ],
  },
};

export const mockProfileActionItems: OnboardingActionSuggestionsChecklistItem[] =
  [
    {
      id: 'uploadCompanyLogo',
      label: 'Upload a company logo',
      isAlreadyComplete: true,
    },
    {
      id: 'uploadCoverPhoto',
      label: 'Upload a cover photo',
      isAlreadyComplete: true,
    },
    {
      id: 'businessOfferings',
      label: 'Add your business offerings',
      isAlreadyComplete: true,
    },
  ] as const;

export const mockReviewsActionItems: OnboardingActionSuggestionsChecklistItem[] =
  [
    {
      id: 'requestReview',
      label: 'Get at least one published review',
      isAlreadyComplete: true,
    },
    {
      id: 'request2Reviews',
      label: 'Request 2 reviews per month',
      isAlreadyComplete: true,
    },
    {
      id: 'replyToAReview',
      label: 'Reply to a review',
      isAlreadyComplete: true,
    },
    {
      id: 'replyToAllReviews',
      label: 'Reply to all your reviews',
      isAlreadyComplete: true,
    },
  ] as const;

export const mockPhotosActionItems: OnboardingActionSuggestionsChecklistItem[] =
  [
    {
      id: 'createAlbum',
      label: 'Create an album',
      isAlreadyComplete: true,
    },
    {
      id: 'addImages',
      label: 'Add at least 4 images to the album',
      isAlreadyComplete: true,
    },
    {
      id: 'tags',
      label:
        'Add tags to your photos to help customers understand more about your projects. You can choose between: before, during or after.',
      isAlreadyComplete: true,
    },
    {
      id: 'uploadRecentPhotos',
      label:
        'Customers love to see recent examples of your work. Aim to upload new photos at least once per month. Upload photos now.',
      isAlreadyComplete: true,
    },
  ] as const;

export const useUserContextMock = useUserContext as jest.Mock;
export const useCompanyContextMock = useCompanyContext as jest.Mock;
export const useReviewsMock = useReviews as jest.Mock<
  ReturnType<typeof useReviews>
>;
