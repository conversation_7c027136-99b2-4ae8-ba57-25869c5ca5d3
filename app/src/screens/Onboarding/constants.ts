import { ParamListBase } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  PHOTOS_SCREEN,
  PROFILE_SCREEN,
  PROFILE_BUSINESS_OFFERINGS_SCREEN,
  REVIEWS_REQUEST,
  REVIEWS_SCREEN,
} from 'src/constants';
import { UserAccessLevel } from 'src/hooks/useUserAccessLevels';
import {
  MyProfileActionAnalyticsKeys,
  OnboardingAnalyticsKeys,
  PhotoActionAnalyticsKeys,
  ReviewsActionAnalyticsKeys,
} from 'src/constants.events';
import {
  MyProfileActionChecklistItemIds,
  MyProfileActionSuggestionCompletionKeys,
  PhotoActionChecklistItemIds,
  PhotoActionSuggestionCompletionKeys,
  ReviewsActionChecklistItemIds,
  ReviewsActionSuggestionCompletionKeys,
} from './types';

export type OnboardingActionSuggestionsChecklistItem<
  TId extends string = string,
  TAnalyticsId extends OnboardingAnalyticsKeys = OnboardingAnalyticsKeys,
> = {
  id: TId;
  label: string;
  isAlreadyComplete: boolean;
  onPress?: () => void;
  analyticsId?: TAnalyticsId;
};

export type ChecklistItemParams<T extends string> = {
  navigation?: NativeStackNavigationProp<ParamListBase>;
  userAccessLevel: UserAccessLevel | null;
  completeState: Record<T, boolean>;
};

export type WidgetProps = {
  progress: number;
  actionItems: OnboardingActionSuggestionsChecklistItem[];
};

/**
 * Builds a checklist of onboarding action suggestions.
 *
 * @template TStep - A readonly array of `OnboardingActionSuggestionsChecklistItem` objects.
 * @template Id - The type of the `id` property of the items in `TStep`.
 *
 * @param {TStep} steps - The array of onboarding action suggestions checklist items.
 * @param {readonly Id[]} [stepIds] - An optional array of step IDs to filter the checklist items.
 *
 * @returns {OnboardingActionSuggestionsChecklistItem[]} - The filtered or complete array of onboarding action suggestions checklist items.
 */
const buildChecklist = <
  TStep extends readonly OnboardingActionSuggestionsChecklistItem[],
  Id extends TStep[number]['id'],
>(
  steps: TStep,
  stepIds?: readonly Id[],
): OnboardingActionSuggestionsChecklistItem[] => {
  if (!stepIds) {
    return [...steps];
  }

  return steps.filter((step) => stepIds.includes(step.id as Id));
};

export const getPhotosActionSuggestionsChecklist = ({
  navigation,
  userAccessLevel,
  completeState: {
    atLeastOneAlbumExists,
    atLeastOneAlbumContains4Images,
    hasAddedBeforeAfterTags,
  },
}: ChecklistItemParams<PhotoActionSuggestionCompletionKeys>): OnboardingActionSuggestionsChecklistItem[] => {
  const steps: OnboardingActionSuggestionsChecklistItem<
    PhotoActionChecklistItemIds,
    PhotoActionAnalyticsKeys
  >[] = [
    {
      id: 'createAlbum',
      label: 'Create an album',
      isAlreadyComplete: atLeastOneAlbumExists,
      onPress: () => navigation?.navigate(PHOTOS_SCREEN),
    },
    {
      id: 'addImages',
      label: 'Add at least 4 images to the album',
      isAlreadyComplete: atLeastOneAlbumContains4Images,
      onPress: () => navigation?.navigate(PHOTOS_SCREEN),
    },
    {
      id: 'tags',
      label:
        'Add tags to your photos to help customers understand more about your projects. You can choose between: before, during or after.',
      isAlreadyComplete: hasAddedBeforeAfterTags,
      onPress: () => navigation?.navigate(PHOTOS_SCREEN),
    },
    {
      id: 'uploadRecentPhotos',
      label:
        'Customers love to see recent examples of your work. Aim to upload new photos at least once per month. Upload photos now.',
      isAlreadyComplete: hasAddedBeforeAfterTags,
      onPress: () => navigation?.navigate(PHOTOS_SCREEN),
    },
  ];

  switch (userAccessLevel) {
    case UserAccessLevel.Pending:
    case UserAccessLevel.ActiveOnboarding:
      return buildChecklist(steps, ['createAlbum', 'addImages', 'tags']);
    case UserAccessLevel.Essentials:
      return buildChecklist(steps);
    default:
      return [];
  }
};

export const getReviewsActionSuggestionsChecklist = ({
  navigation,
  userAccessLevel,
  completeState: {
    atLeastOneReviewExists,
    atLeastTwoReviewsRequestedPerMonth,
    hasRespondedToAtLeastOneReview,
  },
}: ChecklistItemParams<ReviewsActionSuggestionCompletionKeys>): OnboardingActionSuggestionsChecklistItem[] => {
  const steps: OnboardingActionSuggestionsChecklistItem<
    ReviewsActionChecklistItemIds,
    ReviewsActionAnalyticsKeys
  >[] = [
    {
      id: 'requestReview',
      label: 'Get at least one published review',
      isAlreadyComplete: atLeastOneReviewExists,
      onPress: () =>
        navigation?.navigate(REVIEWS_SCREEN, { screen: REVIEWS_REQUEST }),
      analyticsId: 'reviews_request',
    },
    {
      id: 'request2Reviews',
      label: 'Request 2 reviews per month',
      isAlreadyComplete: atLeastTwoReviewsRequestedPerMonth,
      onPress: () =>
        navigation?.navigate(REVIEWS_SCREEN, { screen: REVIEWS_REQUEST }),
      analyticsId: 'reviews_request_2_per_month',
    },
    {
      id: 'replyToAReview',
      label: 'Reply to a review',
      isAlreadyComplete: hasRespondedToAtLeastOneReview,
      onPress: () => navigation?.navigate(REVIEWS_SCREEN),
    },
  ] as const;

  switch (userAccessLevel) {
    case UserAccessLevel.Pending:
    case UserAccessLevel.ActiveOnboarding:
      return buildChecklist(steps, ['requestReview', 'replyToAReview']);
    case UserAccessLevel.Essentials:
      return buildChecklist(steps);
    default:
      return [];
  }
};

export const getMyProfileActionSuggestionsChecklist = ({
  navigation,
  userAccessLevel,
  completeState: { hasProfilePhoto, hasCoverPhoto, hasViewedBusinessOfferings },
}: ChecklistItemParams<MyProfileActionSuggestionCompletionKeys>): OnboardingActionSuggestionsChecklistItem[] => {
  const steps: OnboardingActionSuggestionsChecklistItem<
    MyProfileActionChecklistItemIds,
    MyProfileActionAnalyticsKeys
  >[] = [
    {
      id: 'uploadCompanyLogo',
      label: 'Upload a company logo',
      isAlreadyComplete: hasProfilePhoto,
      onPress: () => navigation?.navigate(PROFILE_SCREEN),
    },
    {
      id: 'uploadCoverPhoto',
      label: 'Upload a cover photo',
      isAlreadyComplete: hasCoverPhoto,
      onPress: () => navigation?.navigate(PROFILE_SCREEN),
    },
    {
      id: 'businessOfferings',
      label: 'Add your business offerings',
      isAlreadyComplete: hasViewedBusinessOfferings,
      onPress: () => navigation?.navigate(PROFILE_BUSINESS_OFFERINGS_SCREEN),
      analyticsId: 'profile_add_business_offering',
    },
  ];

  switch (userAccessLevel) {
    case UserAccessLevel.Pending:
    case UserAccessLevel.ActiveOnboarding:
      return buildChecklist(steps);
    case UserAccessLevel.Essentials:
      return buildChecklist(steps, ['uploadCompanyLogo', 'uploadCoverPhoto']);
    default:
      return [];
  }
};

export const ONBOARDING_MODAL_DISMISSED_PERSIST_KEY =
  'ONBOARDING_MODAL_DISMISSED';
