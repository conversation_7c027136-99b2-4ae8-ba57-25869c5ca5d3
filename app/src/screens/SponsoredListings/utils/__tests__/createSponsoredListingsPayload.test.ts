import { CampaignDtoType } from 'src/data/schemas/api/campaigns/CampaignDtoPageResult';
import { GeographyTypeApiEnum } from 'src/data/schemas/api/campaigns';
import {
  mapBidStrategy,
  createSponsoredListingsApiPayload,
} from '../createSponsoredListingsPayload';
import {
  BidStrategy,
  AdvancedSettingsState,
} from '../../context/CreateSponsoredListingsContext';
import { SponsoredListingPayload } from '../../types';

describe('mapBidStrategy', () => {
  it('should map PLAYER bid strategy to player', () => {
    expect(mapBidStrategy(BidStrategy.PLAYER)).toBe('player');
  });

  it('should map COMPETITOR bid strategy to competitor', () => {
    expect(mapBidStrategy(BidStrategy.COMPETITOR)).toBe('competitor');
  });

  it('should map LEADER bid strategy to leader', () => {
    expect(mapBidStrategy(BidStrategy.LEADER)).toBe('leader');
  });

  it('should return leader as default when bid strategy is undefined', () => {
    expect(mapBidStrategy(undefined)).toBe('leader');
  });

  it('should handle invalid bid strategy by returning leader', () => {
    expect(mapBidStrategy('INVALID_STRATEGY' as BidStrategy)).toBe('leader');
  });
});

describe('createSponsoredListingsApiPayload', () => {
  const mockDate = new Date('2025-01-01');
  const originalDate = global.Date;
  beforeAll(() => {
    global.Date = jest.fn(() => mockDate) as unknown as typeof globalThis.Date;
    global.Date.UTC = originalDate.UTC;
    global.Date.parse = originalDate.parse;
    global.Date.now = originalDate.now;
  });

  afterAll(() => {
    global.Date = originalDate;
  });

  const mockPayload: SponsoredListingPayload = {
    campaignId: '12345',
    budget: '100.50',
    bidStrategy: BidStrategy.LEADER,
    subcategories: [
      { id: '101', name: 'Plumbing' },
      { id: '102', name: 'Electrical' },
    ],
    geos: [
      {
        id: '1',
        name: 'London',
        type: GeographyTypeApiEnum.PostcodeArea,
        postCode: 'SW',
      },
      {
        id: '2',
        name: 'East London',
        type: GeographyTypeApiEnum.PostcodeDistrict,
        postCode: 'E1',
      },
    ],
  };

  const mockCampaign = {
    campaignId: '12345',
    category: {
      categoryId: 500,
      name: 'Test Category',
      isSearchable: true,
    },
  } as unknown as CampaignDtoType;

  const mockAdvancedSettings: AdvancedSettingsState = {
    isOpen: false,
    editMode: null,
    delayStartEnabled: false,
    delayStartDate: null,
    endDateEnabled: false,
    endDate: null,
    manualBidListingsEnabled: false,
    manualBidListingsAmount: '',
    manualBidRaQEnabled: false,
    manualBidRaQAmount: '',
    listingPacingEnabled: false,
    listingPacingValue: '',
  };

  it('should create API payload with basic settings', () => {
    const result = createSponsoredListingsApiPayload(
      mockPayload,
      mockCampaign,
      mockAdvancedSettings,
    );

    expect(result).toHaveLength(2);
    expect(result[0].type).toBe('MdpSponsoredSearch');
    expect(result[1].type).toBe('MdpSponsoredSearch');
    expect(result[0].mdpSponsoredSearch.searchType).toBe('Listings');
    expect(result[1].mdpSponsoredSearch.searchType).toBe('RequestAQuote');
    // Budget should be split 70/30 as it's >= £100
    expect(result[0].maxBudget).toBe(100.5 * 0.7); // 70% for Listings
    expect(result[1].maxBudget).toBe(100.5 * 0.3); // 30% for RequestAQuote
    expect(result[0].budgetPeriod).toEqual(mockDate);
  });

  it('should handle category data correctly', () => {
    const result = createSponsoredListingsApiPayload(
      mockPayload,
      mockCampaign,
      mockAdvancedSettings,
    );

    expect(result[0].category.categoryId).toBe(500);
    expect(result[0].category.isSearchable).toBe(true);
  });

  it('should handle subcategories correctly', () => {
    const result = createSponsoredListingsApiPayload(
      mockPayload,
      mockCampaign,
      mockAdvancedSettings,
    );

    expect(result[0].subCategories).toHaveLength(2);
    expect(result[0].subCategories[0].categoryId).toBe(101);
    expect(result[0].subCategories[0].parentCategoryId).toBe(500);
  });

  it('should handle geographies correctly', () => {
    const result = createSponsoredListingsApiPayload(
      mockPayload,
      mockCampaign,
      mockAdvancedSettings,
    );

    expect(result[0].geographies).toHaveLength(2);
    expect(result[0].geographies[0]).toEqual({
      type: GeographyTypeApiEnum.PostcodeArea,
      value: 'SW',
    });
    expect(result[0].geographies[1]).toEqual({
      type: GeographyTypeApiEnum.PostcodeDistrict,
      value: 'E1',
    });
  });

  it('should set pausedUntil when delayStartEnabled is true', () => {
    const delayDate = new Date('2025-02-01');
    const advancedSettingsWithDelay = {
      ...mockAdvancedSettings,
      delayStartEnabled: true,
      delayStartDate: delayDate,
    };

    const result = createSponsoredListingsApiPayload(
      mockPayload,
      mockCampaign,
      advancedSettingsWithDelay,
    );

    expect(result[0].pausedUntil).toBe(delayDate);
    expect(result[1].pausedUntil).toBe(delayDate);
  });

  it('should set endDate when endDateEnabled is true', () => {
    const endDate = new Date('2025-05-01');
    const advancedSettingsWithEndDate = {
      ...mockAdvancedSettings,
      endDateEnabled: true,
      endDate: endDate,
    };

    const result = createSponsoredListingsApiPayload(
      mockPayload,
      mockCampaign,
      advancedSettingsWithEndDate,
    );

    expect(result[0].mdpSponsoredSearch.endDate).toBe(endDate);
    expect(result[1].mdpSponsoredSearch.endDate).toBe(endDate);
  });

  it('should set bidAmount when manualBidListingsEnabled is true', () => {
    const advancedSettingsWithManualBid = {
      ...mockAdvancedSettings,
      manualBidListingsEnabled: true,
      manualBidListingsAmount: '5.75',
    };

    const result = createSponsoredListingsApiPayload(
      mockPayload,
      mockCampaign,
      advancedSettingsWithManualBid,
    );

    expect(result[0].mdpSponsoredSearch.bidAmount).toBe(5.75);
    expect(result[1].mdpSponsoredSearch.bidAmount).toBeNull();
  });

  it('should set bidAmount when manualBidRaqEnabled is true', () => {
    const advancedSettingsWithManualBid = {
      ...mockAdvancedSettings,
      manualBidRaQEnabled: true,
      manualBidRaQAmount: '5.75',
    };

    const result = createSponsoredListingsApiPayload(
      mockPayload,
      mockCampaign,
      advancedSettingsWithManualBid,
    );

    expect(result[0].mdpSponsoredSearch.bidAmount).toBeNull();
    expect(result[1].mdpSponsoredSearch.bidAmount).toBe(5.75);
  });

  it('should handle null campaign gracefully', () => {
    const result = createSponsoredListingsApiPayload(
      mockPayload,
      null,
      mockAdvancedSettings,
    );

    expect(result[0].category.categoryId).toBe(0);
    expect(result[0].category.isSearchable).toBe(true);
  });

  it('should handle empty payload values gracefully', () => {
    const emptyPayload: SponsoredListingPayload = {
      campaignId: '',
      budget: '',
      bidStrategy: undefined,
      subcategories: [],
      geos: [],
    };

    const result = createSponsoredListingsApiPayload(
      emptyPayload,
      null,
      mockAdvancedSettings,
    );

    expect(result[0].maxBudget).toBe(0);
    expect(result[0].subCategories).toHaveLength(0);
    expect(result[0].geographies).toHaveLength(0);
    expect(result[0].mdpSponsoredSearch.primaryCampaignId).toBe('');
    expect(result[0].mdpSponsoredSearch.bidStrategy).toBe('leader');
  });

  it('should handle geographies with mixed PostcodeArea and PostcodeDistrict types', () => {
    const mixedGeosPayload = {
      ...mockPayload,
      geos: [
        {
          id: '1',
          name: 'North London',
          type: GeographyTypeApiEnum.PostcodeArea,
          postCode: 'N',
        },
        {
          id: '2',
          name: 'East London District 1',
          type: GeographyTypeApiEnum.PostcodeDistrict,
          postCode: 'E1',
        },
        {
          id: '3',
          name: 'South London',
          type: GeographyTypeApiEnum.PostcodeArea,
          postCode: 'SE',
        },
      ],
    };

    const result = createSponsoredListingsApiPayload(
      mixedGeosPayload,
      mockCampaign,
      mockAdvancedSettings,
    );

    expect(result[0].geographies).toHaveLength(3);
    expect(result[0].geographies[0]).toEqual({
      type: GeographyTypeApiEnum.PostcodeArea,
      value: 'N',
    });
    expect(result[0].geographies[1]).toEqual({
      type: GeographyTypeApiEnum.PostcodeDistrict,
      value: 'E1',
    });
    expect(result[0].geographies[2]).toEqual({
      type: GeographyTypeApiEnum.PostcodeArea,
      value: 'SE',
    });
  });

  it('should maintain geography types when transforming to API payload', () => {
    const result = createSponsoredListingsApiPayload(
      mockPayload,
      mockCampaign,
      mockAdvancedSettings,
    );

    expect(result[0].geographies).toHaveLength(2);
    expect(result[0].geographies[0]).toEqual({
      type: GeographyTypeApiEnum.PostcodeArea,
      value: 'SW',
    });
    expect(result[0].geographies[1]).toEqual({
      type: GeographyTypeApiEnum.PostcodeDistrict,
      value: 'E1',
    });
  });

  it('should handle empty geographies array', () => {
    const noGeosPayload = {
      ...mockPayload,
      geos: [],
    };

    const result = createSponsoredListingsApiPayload(
      noGeosPayload,
      mockCampaign,
      mockAdvancedSettings,
    );

    expect(result[0].geographies).toEqual([]);
  });

  it('should split budget 50/50 when budget is less than £100', () => {
    const lowBudgetPayload = {
      ...mockPayload,
      budget: '99.99',
    };

    const result = createSponsoredListingsApiPayload(
      lowBudgetPayload,
      mockCampaign,
      mockAdvancedSettings,
    );

    expect(result[0].mdpSponsoredSearch.searchType).toBe('Listings');
    expect(result[1].mdpSponsoredSearch.searchType).toBe('RequestAQuote');
    expect(result[0].maxBudget).toBe(99.99 * 0.5); // 50% for Listings
    expect(result[1].maxBudget).toBe(99.99 * 0.5); // 50% for RequestAQuote
  });

  it('should split budget 70/30 when budget is £100 or more', () => {
    const highBudgetPayload = {
      ...mockPayload,
      budget: '200',
    };

    const result = createSponsoredListingsApiPayload(
      highBudgetPayload,
      mockCampaign,
      mockAdvancedSettings,
    );

    expect(result[0].mdpSponsoredSearch.searchType).toBe('Listings');
    expect(result[1].mdpSponsoredSearch.searchType).toBe('RequestAQuote');
    expect(result[0].maxBudget).toBe(200 * 0.7); // 70% for Listings
    expect(result[1].maxBudget).toBe(200 * 0.3); // 30% for RequestAQuote
  });
});
