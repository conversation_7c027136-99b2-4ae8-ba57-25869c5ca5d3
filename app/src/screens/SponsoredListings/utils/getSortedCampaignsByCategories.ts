import { CampaignDtoType } from 'src/data/schemas/api/campaigns';
import { GroupedSponsoredListings } from '../types';

export const getSortedCampaignsByCategories = (
  campaigns: CampaignDtoType[],
): { [categoryName: string]: CampaignDtoType[] } => {
  const grouped: GroupedSponsoredListings = {};

  campaigns.forEach((campaign) => {
    const categoryName = campaign.category?.name || 'Other';
    if (!grouped[categoryName]) {
      grouped[categoryName] = [];
    }

    grouped[categoryName].push(campaign);
  });

  Object.keys(grouped).forEach((categoryName) => {
    grouped[categoryName].sort((a, b) => {
      const aIsActive = a.isActive && !a.isPaused;
      const bIsActive = b.isActive && !b.isPaused;

      if (aIsActive && !bIsActive) {
        return -1;
      }

      if (!aIsActive && bIsActive) {
        return 1;
      }

      return 0;
    });
  });

  return grouped;
};
