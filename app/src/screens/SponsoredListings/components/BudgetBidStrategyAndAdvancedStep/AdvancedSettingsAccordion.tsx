import React, {
  ReactElement,
  useState,
  useEffect,
  useCallback,
  useMemo,
} from 'react';
import { View, TouchableOpacity, Modal } from 'react-native';
import {
  Typo<PERSON>,
  Icon,
  Button,
  Slider,
  createSliderIntegerRange,
} from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  palette,
  spacing,
} from '@cat-home-experts/react-native-utilities';
import { useIsAtMostSmallScreenWidth } from 'src/hooks/useMediaQuery';
import { DatePicker } from 'src/components/primitives/DatePicker/DatePicker';
import { useCreateSponsoredListings } from '../../context/CreateSponsoredListingsContext';
import { useGetAdManagerExperiences } from '../../hooks/useGetAdManagerExperiences';
import { extractBidLimits } from '../../utils/getBidRanges';
import { CREATE_SPONSORED_LISTING, BUDGET_PACING } from '../../constants';
import { validateBidAmount } from '../../utils/validateBidAmount';
import { formatCurrencyAmount } from '../../utils/formatCurrencyAmount';
import {
  getTodayStartOfDay,
  validateStartDate as validateStartDateUtil,
  validateEndDate as validateEndDateUtil,
} from '../../utils/dateValidation';
import { PacingOption } from '../../types';

type EditMode =
  | typeof CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.DELAY_START
  | typeof CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.END_DATE
  | typeof CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.MANUAL_BID_LISTING
  | typeof CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.MANUAL_BID_RAQ
  | typeof CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.LISTING_PACING
  | null;

// Create test IDs for component
const TEST_IDS = {
  ROOT: 'advanced-settings-accordion-root',
  DELAY_START_SWITCH: 'advanced-settings-accordion-delay-start-switch',
  END_DATE_SWITCH: 'advanced-settings-accordion-end-date-switch',
  MANUAL_BID_SWITCH: 'advanced-settings-accordion-manual-bid-switch',
};

export function AdvancedSettingsAccordion(): ReactElement {
  // This is requested by business due to dependency on Koddi
  // but once that will be resolved, we re-introduce the bids
  const SHOW_BIDS_TEMPORARILY = false;

  // Temporary flag to hide listing pacing until backend is ready
  const HIDE_LISTING_PACING = true;

  const isMobileScreen = useIsAtMostSmallScreenWidth();

  const {
    advancedSettings,
    toggleAdvancedSettingsOpen,
    updateDelayStartDate,
    updateEndDate,
    updateManualBidListings,
    updateManualBidRaQ,
    updateListingPacing,
  } = useCreateSponsoredListings();

  const { stats: experiencesData } = useGetAdManagerExperiences();
  const bidLimits = extractBidLimits(experiencesData);

  const listingsMinBid = bidLimits.sponsoredListings.minBid;
  const listingsMaxBid = bidLimits.sponsoredListings.maxBid;
  const raqMinBid = bidLimits.requestAQuote.minBid;
  const raqMaxBid = bidLimits.requestAQuote.maxBid;

  /**
   * No-operation function for required callbacks that don't need to do anything
   */
  const noop = () => {
    /* No operation needed */
  };

  const [localEditMode, setLocalEditMode] = useState<EditMode>(null);

  const [tempDelayStartDate, setTempDelayStartDate] = useState<
    Date | undefined
  >(advancedSettings.delayStartDate || undefined);
  const [tempEndDate, setTempEndDate] = useState<Date | undefined>(
    advancedSettings.endDate || undefined,
  );
  const [tempManualBidListingsAmount, setTempManualBidListingsAmount] =
    useState<string>(advancedSettings.manualBidListingsAmount || '');
  const [tempManualBidRaQAmount, setTempManualBidRaQAmount] = useState<string>(
    advancedSettings.manualBidRaQAmount || '',
  );
  const [tempListingPacingValue, setTempListingPacingValue] = useState<string>(
    advancedSettings.listingPacingValue || '',
  );

  // We only need to track if the bids are valid for the save button
  const [isBidListingsValid, setIsBidListingsValid] = useState<boolean>(true);
  const [isBidRaQValid, setIsBidRaQValid] = useState<boolean>(true);

  // Date validation states
  const [endDateError, setEndDateError] = useState<string | undefined>(
    undefined,
  );
  const [isStartDateValid, setIsStartDateValid] = useState<boolean>(true);
  const [isEndDateValid, setIsEndDateValid] = useState<boolean>(true);

  // Validate start date when it changes or when end date changes
  const validateStartDate = useCallback(
    (startDate: Date | undefined) => {
      // Only use the end date for validation if it's enabled
      const endDateForValidation = advancedSettings.endDateEnabled
        ? tempEndDate || advancedSettings.endDate
        : null;

      const result = validateStartDateUtil(startDate, endDateForValidation);
      setIsStartDateValid(result.isValid);
      return result.isValid;
    },
    [
      tempEndDate,
      advancedSettings.endDateEnabled,
      advancedSettings.endDate,
      setIsStartDateValid,
    ],
  );

  // Validate end date when it changes or when start date changes
  const validateEndDate = useCallback(
    (endDate: Date | undefined) => {
      // Only use the delay start date for validation if it's enabled
      const startDateForValidation = advancedSettings.delayStartEnabled
        ? tempDelayStartDate || advancedSettings.delayStartDate
        : null;

      const result = validateEndDateUtil(endDate, startDateForValidation);
      setEndDateError(result.errorMessage);
      setIsEndDateValid(result.isValid);
      return result.isValid;
    },
    [
      tempDelayStartDate,
      advancedSettings.delayStartEnabled,
      advancedSettings.delayStartDate,
      setEndDateError,
      setIsEndDateValid,
    ],
  );

  // Effect to validate dates when they change
  useEffect(() => {
    if (
      localEditMode === CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.DELAY_START
    ) {
      validateStartDate(tempDelayStartDate);
    }
  }, [tempDelayStartDate, localEditMode, validateStartDate]);

  useEffect(() => {
    if (localEditMode === CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.END_DATE) {
      validateEndDate(tempEndDate);
    }
  }, [tempEndDate, localEditMode, validateEndDate]);

  const createBidSliderHandler =
    (
      setAmount: React.Dispatch<React.SetStateAction<string>>,
      setValid: React.Dispatch<React.SetStateAction<boolean>>,
    ) =>
    (value: number) => {
      const formattedValue = value.toFixed(2);
      setAmount(formattedValue);
      setValid(true);
    };

  const listingsSliderSteps = useMemo(() => {
    return listingsMinBid !== undefined && listingsMaxBid !== undefined
      ? createSliderIntegerRange(listingsMinBid, listingsMaxBid, 0.1)
      : [];
  }, [listingsMinBid, listingsMaxBid]);

  const raqSliderSteps = useMemo(() => {
    return raqMinBid !== undefined && raqMaxBid !== undefined
      ? createSliderIntegerRange(raqMinBid, raqMaxBid, 0.1)
      : [];
  }, [raqMinBid, raqMaxBid]);

  const handleBidListingsSliderChange = createBidSliderHandler(
    setTempManualBidListingsAmount,
    setIsBidListingsValid,
  );
  const handleBidRaQSliderChange = createBidSliderHandler(
    setTempManualBidRaQAmount,
    setIsBidRaQValid,
  );

  const handlePacingOptionChange = (option: PacingOption) => {
    updateListingPacing(true, option);
    setLocalEditMode(null);
  };

  const handleStartEdit = (mode: Exclude<EditMode, null>) => {
    if (
      mode === CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.MANUAL_BID_LISTING &&
      (listingsMinBid === undefined || listingsMaxBid === undefined)
    ) {
      return;
    }

    if (
      mode === CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.MANUAL_BID_RAQ &&
      (raqMinBid === undefined || raqMaxBid === undefined)
    ) {
      return;
    }

    setLocalEditMode(mode);

    if (mode === CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.DELAY_START) {
      setTempDelayStartDate(advancedSettings.delayStartDate || undefined);
      // Reset validation states
      setIsStartDateValid(true);

      // Validate end date if it exists, since start date might have changed
      if (advancedSettings.endDate) {
        validateEndDate(advancedSettings.endDate);
      }
    } else if (mode === CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.END_DATE) {
      setTempEndDate(advancedSettings.endDate || undefined);
      // Reset validation states
      setEndDateError(undefined);
      setIsEndDateValid(true);

      // When opening end date picker, ensure we're using the current state from context
      // not the temporary state that might be stale
      if (
        advancedSettings.delayStartEnabled &&
        advancedSettings.delayStartDate
      ) {
        setTempDelayStartDate(advancedSettings.delayStartDate);
      } else {
        setTempDelayStartDate(undefined);
      }

      // Validate start date if it exists, since end date might have changed
      if (advancedSettings.delayStartDate) {
        validateStartDate(advancedSettings.delayStartDate);
      }
    } else if (
      mode === CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.MANUAL_BID_LISTING
    ) {
      // Initialize with current value or max bid value
      const initialValue =
        advancedSettings.manualBidListingsAmount ||
        (listingsMaxBid ? listingsMaxBid.toFixed(2) : '');
      setTempManualBidListingsAmount(initialValue);
      setIsBidListingsValid(true);

      // If there's no current value, trigger the slider change handler to ensure
      // the value is registered even if the user doesn't move the slider
      if (!advancedSettings.manualBidListingsAmount && listingsMaxBid) {
        handleBidListingsSliderChange(listingsMaxBid);
      }
    } else if (
      mode === CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.MANUAL_BID_RAQ
    ) {
      // Initialize with current value or max bid value
      const initialValue =
        advancedSettings.manualBidRaQAmount ||
        (raqMaxBid ? raqMaxBid.toFixed(2) : '');
      setTempManualBidRaQAmount(initialValue);
      setIsBidRaQValid(true);

      // If there's no current value, trigger the slider change handler to ensure
      // the value is registered even if the user doesn't move the slider
      if (!advancedSettings.manualBidRaQAmount && raqMaxBid) {
        handleBidRaQSliderChange(raqMaxBid);
      }
    } else if (
      mode === CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.LISTING_PACING
    ) {
      setTempListingPacingValue(
        advancedSettings.listingPacingValue || PacingOption.EVENLY,
      );
    }
  };

  const handleCancel = () => {
    setLocalEditMode(null);
    setIsBidListingsValid(true);
    setIsBidRaQValid(true);
    setEndDateError(undefined);
    setIsStartDateValid(true);
    setIsEndDateValid(true);
    setTempListingPacingValue(advancedSettings.listingPacingValue || '');
  };

  const handleSave = () => {
    if (
      localEditMode ===
      CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.MANUAL_BID_LISTING
    ) {
      // If no value was set, use the max bid value
      const valueToSave =
        tempManualBidListingsAmount ||
        (listingsMaxBid ? listingsMaxBid.toFixed(2) : '');

      const { isValid } = validateBidAmount(
        valueToSave,
        listingsMinBid,
        listingsMaxBid,
      );
      if (!isValid) {
        return;
      }

      updateManualBidListings(
        Boolean(valueToSave),
        valueToSave ? formatCurrencyAmount(valueToSave) : '',
      );
    } else if (
      localEditMode === CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.MANUAL_BID_RAQ
    ) {
      // If no value was set, use the max bid value
      const valueToSave =
        tempManualBidRaQAmount || (raqMaxBid ? raqMaxBid.toFixed(2) : '');

      const { isValid } = validateBidAmount(valueToSave, raqMinBid, raqMaxBid);
      if (!isValid) {
        return;
      }

      updateManualBidRaQ(
        Boolean(valueToSave),
        valueToSave ? formatCurrencyAmount(valueToSave) : '',
      );
    } else if (
      localEditMode === CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.DELAY_START
    ) {
      // Validate start date before saving
      const startDateValid = validateStartDate(tempDelayStartDate);
      if (!startDateValid) {
        return;
      }

      updateDelayStartDate(Boolean(tempDelayStartDate), tempDelayStartDate);

      // If end date is enabled, validate it against the new start date
      if (advancedSettings.endDateEnabled && advancedSettings.endDate) {
        validateEndDate(advancedSettings.endDate);
      }
    } else if (
      localEditMode === CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.END_DATE
    ) {
      // Validate end date before saving
      const endDateValid = validateEndDate(tempEndDate);
      if (!endDateValid) {
        return;
      }

      updateEndDate(Boolean(tempEndDate), tempEndDate);

      // If start date is enabled, validate it against the new end date
      if (
        advancedSettings.delayStartEnabled &&
        advancedSettings.delayStartDate
      ) {
        validateStartDate(advancedSettings.delayStartDate);
      }
    }

    setIsBidListingsValid(true);
    setIsBidRaQValid(true);
    setEndDateError(undefined);
    setIsStartDateValid(true);
    setIsEndDateValid(true);
    setTempListingPacingValue(advancedSettings.listingPacingValue || '');
    setLocalEditMode(null);
  };

  return (
    <View style={styles.cardContainer} testID={TEST_IDS.ROOT}>
      <TouchableOpacity
        style={styles.cardHeader}
        onPress={() => toggleAdvancedSettingsOpen(!advancedSettings.isOpen)}
      >
        <View style={styles.cardTitleContainer}>
          <View style={styles.iconCircle}>
            <Icon name="settings-cogs-fill" size={20} />
          </View>
          <Typography
            useVariant="bodySemiBold"
            style={isMobileScreen ? styles.cardTitleMobile : styles.cardTitle}
          >
            {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.ADVANCED_LISTING_SETTINGS}
          </Typography>
        </View>
        <View style={styles.cardActions}>
          <Icon
            name={advancedSettings.isOpen ? 'chevron-up' : 'chevron-down'}
            size={20}
            color={palette.mortarV3.tokenNeutral900}
          />
        </View>
      </TouchableOpacity>

      {advancedSettings.isOpen && (
        <View style={styles.advancedSettingsContent}>
          <View style={styles.divider} />

          {/* Only show the manual bid listings section if bid limits are available */}
          {SHOW_BIDS_TEMPORARILY &&
            listingsMinBid !== undefined &&
            listingsMaxBid !== undefined && (
              <>
                <View style={styles.advancedSettingItem}>
                  <View style={styles.advancedSettingHeader}>
                    <View style={styles.iconCircle}>
                      <Icon name="account-cash" size={20} />
                    </View>
                    <Typography
                      useVariant="bodySemiBold"
                      style={styles.advancedSettingTitle}
                    >
                      {
                        CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                          .SET_MANUAL_BID_FOR_LISTING
                      }
                    </Typography>
                  </View>

                  <Typography
                    useVariant="bodyRegular"
                    style={styles.advancedSettingDescription}
                  >
                    {
                      CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                        .SET_CUSTOM_BID_AMOUNT_LISTING
                    }
                  </Typography>

                  {advancedSettings.manualBidListingsEnabled ? (
                    <View style={styles.selectedValueContainer}>
                      <View style={styles.valueWithRemoveContainer}>
                        <TouchableOpacity
                          style={styles.editValueButton}
                          onPress={() =>
                            handleStartEdit(
                              CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                                .MANUAL_BID_LISTING,
                            )
                          }
                        >
                          <Typography useVariant="bodyRegular">
                            {
                              CREATE_SPONSORED_LISTING.BUDGET_CARD
                                .CURRENCY_SYMBOL
                            }
                            {formatCurrencyAmount(
                              advancedSettings.manualBidListingsAmount || '',
                            )}
                          </Typography>
                        </TouchableOpacity>
                        <TouchableOpacity
                          onPress={() => updateManualBidListings(false, '')}
                          style={styles.removeLink}
                        >
                          <Typography
                            useVariant="textLinkSMRegular"
                            style={styles.removeLinkText}
                          >
                            {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.REMOVE}
                          </Typography>
                        </TouchableOpacity>
                      </View>
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={styles.enableButton}
                      onPress={() =>
                        handleStartEdit(
                          CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                            .MANUAL_BID_LISTING,
                        )
                      }
                      testID={TEST_IDS.MANUAL_BID_SWITCH}
                    >
                      <Typography
                        useVariant="bodyRegular"
                        style={styles.enableButtonText}
                      >
                        {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.SET}
                      </Typography>
                    </TouchableOpacity>
                  )}
                  <View style={styles.infoBox}>
                    <Icon
                      name="info"
                      size={16}
                      color={palette.mortar.tokenColorSystemLinkBlue}
                      style={styles.infoIcon}
                    />
                    <Typography useVariant="bodySmall" style={styles.infoText}>
                      {
                        CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                          .LISTINGS_BID_RANGE
                      }
                      {listingsMinBid.toFixed(2)}
                      {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.RANGE_SEPARATOR}
                      {listingsMaxBid.toFixed(2)}
                    </Typography>
                  </View>
                </View>

                <View style={styles.divider} />
              </>
            )}

          {/* Only show the manual bid RaQ section if bid limits are available */}
          {SHOW_BIDS_TEMPORARILY &&
            raqMinBid !== undefined &&
            raqMaxBid !== undefined && (
              <>
                <View style={styles.advancedSettingItem}>
                  <View style={styles.advancedSettingHeader}>
                    <View style={styles.iconCircle}>
                      <Icon name="account-cash" size={20} />
                    </View>
                    <Typography
                      useVariant="bodySemiBold"
                      style={styles.advancedSettingTitle}
                    >
                      {
                        CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                          .SET_MANUAL_BID_FOR_RAQ
                      }
                    </Typography>
                  </View>

                  <Typography
                    useVariant="bodyRegular"
                    style={styles.advancedSettingDescription}
                  >
                    {
                      CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                        .SET_CUSTOM_BID_AMOUNT_RAQ
                    }
                  </Typography>

                  {advancedSettings.manualBidRaQEnabled ? (
                    <View style={styles.selectedValueContainer}>
                      <View style={styles.valueWithRemoveContainer}>
                        <TouchableOpacity
                          style={styles.editValueButton}
                          onPress={() =>
                            handleStartEdit(
                              CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                                .MANUAL_BID_RAQ,
                            )
                          }
                        >
                          <Typography useVariant="bodyRegular">
                            {
                              CREATE_SPONSORED_LISTING.BUDGET_CARD
                                .CURRENCY_SYMBOL
                            }
                            {formatCurrencyAmount(
                              advancedSettings.manualBidRaQAmount || '',
                            )}
                          </Typography>
                        </TouchableOpacity>
                        <TouchableOpacity
                          onPress={() => updateManualBidRaQ(false, '')}
                          style={styles.removeLink}
                        >
                          <Typography
                            useVariant="textLinkSMRegular"
                            style={styles.removeLinkText}
                          >
                            {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.REMOVE}
                          </Typography>
                        </TouchableOpacity>
                      </View>
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={styles.enableButton}
                      onPress={() =>
                        handleStartEdit(
                          CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                            .MANUAL_BID_RAQ,
                        )
                      }
                    >
                      <Typography
                        useVariant="bodyRegular"
                        style={styles.enableButtonText}
                      >
                        {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.SET}
                      </Typography>
                    </TouchableOpacity>
                  )}
                  <View style={styles.infoBox}>
                    <Icon
                      name="info"
                      size={16}
                      color={palette.mortar.tokenColorSystemLinkBlue}
                      style={styles.infoIcon}
                    />
                    <Typography useVariant="bodySmall" style={styles.infoText}>
                      {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.RAQ_BID_RANGE}
                      {raqMinBid.toFixed(2)}
                      {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.RANGE_SEPARATOR}
                      {raqMaxBid.toFixed(2)}
                    </Typography>
                  </View>
                </View>

                <View style={styles.divider} />
              </>
            )}

          <View style={styles.advancedSettingItem}>
            <View style={styles.advancedSettingHeader}>
              <View style={styles.iconCircle}>
                <Icon name="clock-clockwise" size={20} />
              </View>
              <Typography
                useVariant="bodySemiBold"
                style={styles.advancedSettingTitle}
              >
                {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.DELAY_START_DATE}
              </Typography>
            </View>

            <Typography
              useVariant="bodyRegular"
              style={styles.advancedSettingDescription}
            >
              {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.CHOOSE_WHEN_RUNNING}
            </Typography>

            {advancedSettings.delayStartEnabled ? (
              <View style={styles.selectedValueContainer}>
                <View style={styles.valueWithRemoveContainer}>
                  <TouchableOpacity
                    style={styles.editValueButton}
                    onPress={() => handleStartEdit('delay-start')}
                  >
                    <Typography useVariant="bodyRegular">
                      {advancedSettings.delayStartDate
                        ? advancedSettings.delayStartDate.toLocaleDateString()
                        : CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.SELECT_DATE}
                    </Typography>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => updateDelayStartDate(false, null)}
                    style={styles.removeLink}
                  >
                    <Typography
                      useVariant="textLinkSMRegular"
                      style={styles.removeLinkText}
                    >
                      {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.REMOVE}
                    </Typography>
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <TouchableOpacity
                style={styles.enableButton}
                onPress={() => handleStartEdit('delay-start')}
                testID={TEST_IDS.DELAY_START_SWITCH}
              >
                <Typography
                  useVariant="bodyRegular"
                  style={styles.enableButtonText}
                >
                  {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.SET}
                </Typography>
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.divider} />

          <View style={styles.advancedSettingItem}>
            <View style={styles.advancedSettingHeader}>
              <View style={styles.iconCircle}>
                <Icon name="clock" size={20} />
              </View>
              <Typography
                useVariant="bodySemiBold"
                style={styles.advancedSettingTitle}
              >
                {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.SET_END_DATE}
              </Typography>
            </View>

            <Typography
              useVariant="bodyRegular"
              style={styles.advancedSettingDescription}
            >
              {
                CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                  .CHOOSE_WHEN_STOP_RUNNING
              }
            </Typography>

            {advancedSettings.endDateEnabled ? (
              <View style={styles.selectedValueContainer}>
                <View style={styles.valueWithRemoveContainer}>
                  <TouchableOpacity
                    style={styles.editValueButton}
                    onPress={() => handleStartEdit('end-date')}
                  >
                    <Typography useVariant="bodyRegular">
                      {advancedSettings.endDate
                        ? advancedSettings.endDate.toLocaleDateString()
                        : CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.SELECT_DATE}
                    </Typography>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => updateEndDate(false, null)}
                    style={styles.removeLink}
                  >
                    <Typography
                      useVariant="textLinkSMRegular"
                      style={styles.removeLinkText}
                    >
                      {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.REMOVE}
                    </Typography>
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <TouchableOpacity
                style={styles.enableButton}
                onPress={() => handleStartEdit('end-date')}
                testID={TEST_IDS.END_DATE_SWITCH}
              >
                <Typography
                  useVariant="bodyRegular"
                  style={styles.enableButtonText}
                >
                  {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.SET}
                </Typography>
              </TouchableOpacity>
            )}
          </View>

          {!HIDE_LISTING_PACING && (
            <>
              <View style={styles.divider} />

              <View style={styles.advancedSettingItem}>
                <View style={styles.advancedSettingHeader}>
                  <View style={styles.iconCircle}>
                    <Icon name="timer" size={20} />
                  </View>
                  <Typography
                    useVariant="bodySemiBold"
                    style={styles.advancedSettingTitle}
                  >
                    {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.LISTING_PACING}
                  </Typography>
                </View>

                <Typography
                  useVariant="bodyRegular"
                  style={styles.advancedSettingDescription}
                >
                  {
                    CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                      .LISTING_PACING_DESCRIPTION
                  }
                </Typography>

                {advancedSettings.listingPacingEnabled ? (
                  <View style={styles.selectedValueContainer}>
                    <View style={styles.valueWithRemoveContainer}>
                      <TouchableOpacity
                        style={styles.editValueButton}
                        onPress={() =>
                          handleStartEdit(
                            CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                              .LISTING_PACING,
                          )
                        }
                      >
                        <Typography useVariant="bodyRegular">
                          {advancedSettings.listingPacingValue ||
                            PacingOption.EVENLY}
                        </Typography>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => updateListingPacing(false, '')}
                        style={styles.removeLink}
                      >
                        <Typography
                          useVariant="textLinkSMRegular"
                          style={styles.removeLinkText}
                        >
                          {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.REMOVE}
                        </Typography>
                      </TouchableOpacity>
                    </View>
                  </View>
                ) : (
                  <TouchableOpacity
                    style={styles.enableButton}
                    onPress={() =>
                      handleStartEdit(
                        CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.LISTING_PACING,
                      )
                    }
                  >
                    <Typography
                      useVariant="bodyRegular"
                      style={styles.enableButtonText}
                    >
                      {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.SET_PACING}
                    </Typography>
                  </TouchableOpacity>
                )}
              </View>

              <View style={styles.divider} />
            </>
          )}
        </View>
      )}

      <Modal
        visible={localEditMode !== null}
        transparent={true}
        animationType="slide"
        onRequestClose={handleCancel}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Typography useVariant="bodySemiBold" style={styles.modalTitle}>
                {localEditMode ===
                  CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.DELAY_START &&
                  CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                    .MODAL_TITLE_DELAY_START}
                {localEditMode ===
                  CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.END_DATE &&
                  CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.MODAL_TITLE_END_DATE}
                {localEditMode ===
                  CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.MANUAL_BID_LISTING &&
                  CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                    .MODAL_TITLE_MANUAL_BID_LISTING}
                {localEditMode ===
                  CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.MANUAL_BID_RAQ &&
                  CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                    .MODAL_TITLE_MANUAL_BID_RAQ}
                {!HIDE_LISTING_PACING &&
                  localEditMode ===
                    CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.LISTING_PACING &&
                  CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                    .MODAL_TITLE_LISTING_PACING}
              </Typography>
              <TouchableOpacity onPress={handleCancel}>
                <Icon
                  name="cross"
                  size={24}
                  color={palette.mortarV3.tokenNeutral900}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              {localEditMode ===
                CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.DELAY_START && (
                <>
                  <Typography
                    useVariant="bodyRegular"
                    style={styles.modalDescription}
                  >
                    {
                      CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                        .CHOOSE_WHEN_RUNNING
                    }
                  </Typography>
                  <View style={styles.datePickerContainer}>
                    <DatePicker
                      label={
                        CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                          .START_DATE_LABEL
                      }
                      value={tempDelayStartDate}
                      onDateChanged={(date: Date) => {
                        setTempDelayStartDate(date);
                        validateStartDate(date);
                      }}
                      onDatePickerShown={noop}
                      placeholder="DD, MM, YYYY"
                      minimumDate={getTodayStartOfDay()}
                      maximumDate={
                        advancedSettings.endDateEnabled &&
                        advancedSettings.endDate
                          ? advancedSettings.endDate
                          : undefined
                      }
                      isInvalid={!isStartDateValid}
                    />
                  </View>
                </>
              )}

              {localEditMode ===
                CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.END_DATE && (
                <>
                  <Typography
                    useVariant="bodyRegular"
                    style={styles.modalDescription}
                  >
                    {
                      CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                        .CHOOSE_WHEN_STOP_RUNNING
                    }
                  </Typography>
                  <View style={styles.datePickerContainer}>
                    <DatePicker
                      label={
                        CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.END_DATE_LABEL
                      }
                      value={tempEndDate}
                      onDateChanged={(date: Date) => {
                        setTempEndDate(date);
                        validateEndDate(date);
                      }}
                      onDatePickerShown={noop}
                      placeholder="DD, MM, YYYY"
                      minimumDate={
                        tempDelayStartDate ||
                        (advancedSettings.delayStartEnabled
                          ? advancedSettings.delayStartDate
                          : null) ||
                        getTodayStartOfDay()
                      }
                      isInvalid={!isEndDateValid}
                    />
                    {endDateError && (
                      <Typography
                        useVariant="labelRegular"
                        style={styles.errorMessage}
                      >
                        {endDateError}
                      </Typography>
                    )}
                  </View>
                </>
              )}

              {localEditMode ===
                CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.MANUAL_BID_LISTING &&
                listingsMinBid !== undefined &&
                listingsMaxBid !== undefined && (
                  <>
                    <Typography
                      useVariant="bodyRegular"
                      style={styles.modalDescription}
                    >
                      {
                        CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                          .SET_CUSTOM_BID_AMOUNT_LISTING
                      }
                    </Typography>
                    <View style={styles.manualBidContainer}>
                      <Typography
                        useVariant="headingLGSemiBold"
                        style={styles.selectedBidText}
                      >
                        {CREATE_SPONSORED_LISTING.BUDGET_CARD.CURRENCY_SYMBOL}
                        {tempManualBidListingsAmount ||
                          (listingsMaxBid ? listingsMaxBid.toFixed(2) : '0.00')}
                      </Typography>
                      <View style={styles.sliderContainer}>
                        <Slider
                          value={
                            parseFloat(tempManualBidListingsAmount) ||
                            listingsMaxBid
                          }
                          minimumValue={listingsMinBid}
                          maximumValue={listingsMaxBid}
                          steps={listingsSliderSteps}
                          snapToNotches
                          tapToSeek
                          thumbWidthUnits={3}
                          onChange={handleBidListingsSliderChange}
                          accessibilityName="listings-bid-slider"
                          progressBackgroundColor={
                            palette.mortar.tokenColorPrimaryBlue
                          }
                          thumbInnerBackgroundColor={
                            palette.mortar.tokenColorPrimaryWhite
                          }
                          thumbInnerBorderColor={
                            palette.mortar.tokenColorPrimaryBlue
                          }
                          thumbInnerBorderWidth={0.2}
                        />
                      </View>
                      <View style={styles.sliderLabels}>
                        <Typography useVariant="bodyRegular">
                          {CREATE_SPONSORED_LISTING.BUDGET_CARD.CURRENCY_SYMBOL}
                          {listingsMinBid?.toFixed(2)}
                        </Typography>
                        <Typography useVariant="bodyRegular">
                          {CREATE_SPONSORED_LISTING.BUDGET_CARD.CURRENCY_SYMBOL}
                          {listingsMaxBid?.toFixed(2)}
                        </Typography>
                      </View>
                    </View>
                    <View style={styles.infoBox}>
                      <Icon
                        name="info"
                        size={16}
                        color={palette.mortar.tokenColorSystemLinkBlue}
                        style={styles.infoIcon}
                      />
                      <Typography
                        useVariant="bodySmall"
                        style={styles.infoText}
                      >
                        {
                          CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                            .LISTINGS_BID_RANGE
                        }
                        {listingsMinBid.toFixed(2)}
                        {
                          CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                            .RANGE_SEPARATOR
                        }
                        {listingsMaxBid.toFixed(2)}
                      </Typography>
                    </View>
                  </>
                )}

              {localEditMode ===
                CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.MANUAL_BID_RAQ &&
                raqMinBid !== undefined &&
                raqMaxBid !== undefined && (
                  <>
                    <Typography
                      useVariant="bodyRegular"
                      style={styles.modalDescription}
                    >
                      {
                        CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                          .SET_CUSTOM_BID_AMOUNT_RAQ
                      }
                    </Typography>
                    <View style={styles.manualBidContainer}>
                      <Typography
                        useVariant="headingLGSemiBold"
                        style={styles.selectedBidText}
                      >
                        {CREATE_SPONSORED_LISTING.BUDGET_CARD.CURRENCY_SYMBOL}
                        {tempManualBidRaQAmount ||
                          (raqMaxBid ? raqMaxBid.toFixed(2) : '0.00')}
                      </Typography>
                      <View style={styles.sliderContainer}>
                        <Slider
                          value={
                            parseFloat(tempManualBidRaQAmount) || raqMaxBid
                          }
                          minimumValue={raqMinBid}
                          maximumValue={raqMaxBid}
                          steps={raqSliderSteps}
                          snapToNotches
                          tapToSeek
                          thumbWidthUnits={3}
                          onChange={handleBidRaQSliderChange}
                          accessibilityName="raq-bid-slider"
                          progressBackgroundColor={
                            palette.mortar.tokenColorPrimaryBlue
                          }
                          thumbInnerBackgroundColor={
                            palette.mortar.tokenColorPrimaryWhite
                          }
                          thumbInnerBorderColor={
                            palette.mortar.tokenColorPrimaryBlue
                          }
                          thumbInnerBorderWidth={0.2}
                        />
                      </View>
                      <View style={styles.sliderLabels}>
                        <Typography useVariant="bodyRegular">
                          {CREATE_SPONSORED_LISTING.BUDGET_CARD.CURRENCY_SYMBOL}
                          {raqMinBid?.toFixed(2)}
                        </Typography>
                        <Typography useVariant="bodyRegular">
                          {CREATE_SPONSORED_LISTING.BUDGET_CARD.CURRENCY_SYMBOL}
                          {raqMaxBid?.toFixed(2)}
                        </Typography>
                      </View>
                    </View>
                    <View style={styles.infoBox}>
                      <Icon
                        name="info"
                        size={16}
                        color={palette.mortar.tokenColorSystemLinkBlue}
                        style={styles.infoIcon}
                      />
                      <Typography
                        useVariant="bodySmall"
                        style={styles.infoText}
                      >
                        {CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.RAQ_BID_RANGE}
                        {raqMinBid.toFixed(2)}
                        {
                          CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                            .RANGE_SEPARATOR
                        }
                        {raqMaxBid.toFixed(2)}
                      </Typography>
                    </View>
                  </>
                )}

              {!HIDE_LISTING_PACING &&
                localEditMode ===
                  CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.LISTING_PACING && (
                  <>
                    <Typography
                      useVariant="bodyRegular"
                      style={styles.modalDescription}
                    >
                      {
                        CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                          .LISTING_PACING_MODAL_DESCRIPTION
                      }
                    </Typography>
                    <View style={styles.optionsContainer}>
                      {Object.values(PacingOption).map((option) => (
                        <TouchableOpacity
                          key={option}
                          style={styles.optionItem}
                          onPress={() => handlePacingOptionChange(option)}
                        >
                          <View style={styles.optionContent}>
                            <View
                              style={[
                                styles.radioButton,
                                tempListingPacingValue === option &&
                                  styles.radioButtonSelected,
                              ]}
                            >
                              {tempListingPacingValue === option && (
                                <View style={styles.radioButtonInner} />
                              )}
                            </View>
                            <View style={styles.optionTextContainer}>
                              <Typography
                                useVariant="bodySemiBold"
                                style={styles.optionTitle}
                              >
                                {option}
                              </Typography>
                              <Typography
                                useVariant="bodySmall"
                                style={styles.optionDescription}
                              >
                                {getPacingDescription(option)}
                              </Typography>
                            </View>
                          </View>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </>
                )}

              {(HIDE_LISTING_PACING ||
                localEditMode !==
                  CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.LISTING_PACING) && (
                <Button
                  label={CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.SAVE}
                  onPress={handleSave}
                  isDisabled={
                    (localEditMode ===
                      CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE
                        .MANUAL_BID_LISTING &&
                      !isBidListingsValid) ||
                    (localEditMode ===
                      CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.MANUAL_BID_RAQ &&
                      !isBidRaQValid) ||
                    (localEditMode ===
                      CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.DELAY_START &&
                      !isStartDateValid) ||
                    (localEditMode ===
                      CREATE_SPONSORED_LISTING.LOCAL_EDIT_MODE.END_DATE &&
                      !isEndDateValid)
                  }
                  variant="secondary"
                  block
                />
              )}
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

function getPacingDescription(option: PacingOption): string {
  const pacingDescriptionMap = {
    [PacingOption.EVENLY]: BUDGET_PACING.EVENLY,
    [PacingOption.ASAP]: BUDGET_PACING.ASAP,
    [PacingOption.ACCELERATED]: BUDGET_PACING.ACCELERATED,
  };

  return pacingDescriptionMap[option] || '';
}

AdvancedSettingsAccordion.testIds = TEST_IDS;

const styles = createMortarStyles(() => ({
  cardContainer: {
    marginBottom: spacing(2),
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral300,
    borderRadius: spacing(1),
    overflow: 'hidden',
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing(2),
    paddingVertical: spacing(3),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    borderBottomWidth: 1,
    borderBottomColor: palette.mortarV3.tokenNeutral200,
  },
  cardTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 18,
    color: palette.mortarV3.tokenNeutral900,
  },
  cardTitleMobile: {
    fontSize: 14,
  },
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconCircle: {
    width: spacing(5),
    height: spacing(5),
    borderRadius: spacing(2.5),
    backgroundColor: palette.mortarV3.tokenNeutral100,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing(2),
    display: 'flex',
  },
  divider: {
    height: 1,
    backgroundColor: palette.mortarV3.tokenNeutral200,
    marginVertical: spacing(2),
  },
  advancedSettingsContent: {
    padding: spacing(2),
  },
  advancedSettingItem: {
    marginBottom: spacing(2),
  },
  advancedSettingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing(1),
  },
  advancedSettingTitle: {
    fontSize: 16,
    color: palette.mortarV3.tokenNeutral900,
  },
  advancedSettingDescription: {
    marginBottom: spacing(2),
    color: palette.mortarV3.tokenNeutral600,
  },
  selectedValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing(1),
    padding: spacing(2),
    backgroundColor: palette.mortarV3.tokenNeutral100,
    borderRadius: spacing(2.5),
    alignSelf: 'flex-start',
  },
  valueWithRemoveContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  editValueButton: {
    flex: 1,
    padding: spacing(1),
  },
  removeLink: {
    padding: spacing(1),
  },
  removeLinkText: {
    color: palette.mortar.tokenColorSystemLinkBlue,
  },
  enableButton: {
    alignSelf: 'flex-start',
    borderWidth: 1,
    borderColor: palette.mortar.tokenColorSystemLinkBlue,
    borderRadius: spacing(2.5),
    paddingHorizontal: spacing(3),
    paddingVertical: spacing(1),
  },
  enableButtonText: {
    color: palette.mortar.tokenColorSystemLinkBlue,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing(2),
  },
  modalContent: {
    width: '100%',
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    borderRadius: spacing(1),
    overflow: 'hidden',
    maxWidth: 600,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing(2),
    borderBottomWidth: 1,
    borderBottomColor: palette.mortarV3.tokenNeutral200,
  },
  modalTitle: {
    fontSize: 18,
    color: palette.mortarV3.tokenNeutral900,
  },
  modalBody: {
    padding: spacing(2),
  },
  modalDescription: {
    marginBottom: spacing(2),
    color: palette.mortarV3.tokenNeutral600,
  },
  datePickerContainer: {
    marginBottom: spacing(2),
  },
  manualBidContainer: {
    marginBottom: spacing(1),
    width: '100%',
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: palette.mortarV3.tokenDefault100,
    padding: spacing(1.5),
    borderRadius: spacing(1),
    marginTop: spacing(2),
    marginBottom: spacing(2),
  },
  infoIcon: {
    marginRight: spacing(1),
  },
  infoText: {
    color: palette.mortarV3.tokenDefault700,
  },
  selectedBidText: {
    fontSize: 24,
    marginBottom: spacing(2),
    textAlign: 'center',
    color: palette.mortarV3.tokenNeutral900,
  },
  sliderContainer: {
    width: '100%',
    marginBottom: spacing(2),
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: spacing(2),
  },
  errorMessage: {
    color: palette.mortar.tokenColorPrimaryRed,
    marginTop: spacing(0.5),
    marginLeft: spacing(1),
  },
  pacingOptionsContainer: {
    marginBottom: spacing(2),
  },
  pacingOption: {
    padding: spacing(2),
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral300,
    borderRadius: spacing(1),
    marginBottom: spacing(1),
  },
  pacingOptionSelected: {
    borderColor: palette.mortar.tokenColorSystemLinkBlue,
    backgroundColor: palette.mortarV3.tokenDefault100,
  },
  pacingOptionTitle: {
    marginBottom: spacing(0.5),
    color: palette.mortarV3.tokenNeutral900,
  },
  pacingOptionDescription: {
    color: palette.mortarV3.tokenNeutral600,
  },
  optionsContainer: {
    marginBottom: spacing(2),
  },
  optionItem: {
    marginBottom: spacing(1.5),
    padding: spacing(2),
    borderRadius: 8,
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral300,
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: palette.mortarV3.tokenNeutral400,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing(1.5),
  },
  radioButtonSelected: {
    borderColor: palette.mortar.tokenColorSystemLinkBlue,
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: palette.mortar.tokenColorSystemLinkBlue,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionTitle: {
    marginBottom: spacing(0.5),
    fontWeight: '600',
  },
  optionDescription: {
    color: palette.mortarV3.tokenNeutral600,
    fontSize: 13,
    lineHeight: 18,
  },
}));
