import React from 'react';
import { render } from '@testing-library/react-native';
import { SponsoredListingsStatusTag } from './SponsoredListingsStatusTag';

describe('SponsoredListingsStatusTag', () => {
  describe('Active Status', () => {
    it('renders active status correctly', () => {
      const { getByText, getByTestId } = render(
        <SponsoredListingsStatusTag isActive={true} />,
      );

      expect(getByText('Active')).toBeDefined();
      expect(
        getByTestId('sponsored-campaign-status-tag-container'),
      ).toBeDefined();
      expect(getByTestId('sponsored-campaign-status-tag-icon')).toBeDefined();
      expect(getByTestId('sponsored-campaign-status-tag-text')).toBeDefined();
    });

    it('uses check-circle-fill icon for active status', () => {
      const { getByTestId, getByText } = render(
        <SponsoredListingsStatusTag isActive={true} />,
      );

      const icon = getByTestId('sponsored-campaign-status-tag-icon');
      expect(icon).toBeDefined();
      // Check that the icon text content matches the expected icon name
      expect(getByText('check-circle-fill')).toBeDefined();
    });
  });

  describe('Inactive Status', () => {
    it('renders inactive status correctly', () => {
      const { getByText, getByTestId } = render(
        <SponsoredListingsStatusTag isActive={false} />,
      );

      expect(getByText('Inactive')).toBeDefined();
      expect(
        getByTestId('sponsored-campaign-status-tag-container'),
      ).toBeDefined();
      expect(getByTestId('sponsored-campaign-status-tag-icon')).toBeDefined();
      expect(getByTestId('sponsored-campaign-status-tag-text')).toBeDefined();
    });

    it('uses warning-triangle-fill icon for inactive status', () => {
      const { getByTestId, getByText } = render(
        <SponsoredListingsStatusTag isActive={false} />,
      );

      const icon = getByTestId('sponsored-campaign-status-tag-icon');
      expect(icon).toBeDefined();
      // Check that the icon text content matches the expected icon name
      expect(getByText('warning-triangle-fill')).toBeDefined();
    });
  });

  describe('Custom Test ID', () => {
    it('uses custom testID when provided', () => {
      const customTestId = 'custom-status-tag';
      const { getByTestId } = render(
        <SponsoredListingsStatusTag isActive={true} testID={customTestId} />,
      );

      expect(getByTestId(customTestId)).toBeDefined();
    });

    it('uses default testID when not provided', () => {
      const { getByTestId } = render(
        <SponsoredListingsStatusTag isActive={true} />,
      );

      expect(
        getByTestId('sponsored-campaign-status-tag-container'),
      ).toBeDefined();
    });
  });

  describe('Test IDs Export', () => {
    it('exports testIds correctly', () => {
      expect(SponsoredListingsStatusTag.testIds).toBeDefined();
      expect(SponsoredListingsStatusTag.testIds?.CONTAINER).toBe(
        'sponsored-campaign-status-tag-container',
      );
      expect(SponsoredListingsStatusTag.testIds?.ICON).toBe(
        'sponsored-campaign-status-tag-icon',
      );
      expect(SponsoredListingsStatusTag.testIds?.TEXT).toBe(
        'sponsored-campaign-status-tag-text',
      );
    });
  });

  describe('Styling', () => {
    it('renders icon and text elements', () => {
      const { getByTestId } = render(
        <SponsoredListingsStatusTag isActive={true} />,
      );

      const icon = getByTestId('sponsored-campaign-status-tag-icon');
      const text = getByTestId('sponsored-campaign-status-tag-text');

      expect(icon).toBeDefined();
      expect(text).toBeDefined();
    });
  });
});
