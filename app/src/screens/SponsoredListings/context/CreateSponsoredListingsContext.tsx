import React, {
  createContext,
  useContext,
  useReducer,
  ReactNode,
  useCallback,
} from 'react';
import { CampaignDtoType } from 'src/data/schemas/api/campaigns/CampaignDtoPageResult';
import { SubcategoryType, GeoType, SponsoredListingPayload } from '../types';

// Define advanced settings state type
export type AdvancedSettingsState = {
  isOpen: boolean;
  editMode:
    | 'delay-start'
    | 'end-date'
    | 'manual-bid-listings'
    | 'manual-bid-raq'
    | 'listing-pacing'
    | null;
  delayStartEnabled: boolean;
  delayStartDate: Date | null;
  endDateEnabled: boolean;
  endDate: Date | null;
  manualBidListingsEnabled: boolean;
  manualBidListingsAmount: string;
  manualBidRaQEnabled: boolean;
  manualBidRaQAmount: string;
  listingPacingEnabled: boolean;
  listingPacingValue: string;
};

// Bid strategy options
export enum BidStrategy {
  PLAYER = 'Player',
  COMPETITOR = 'Competitor',
  LEADER = 'Leader',
}

// Define the steps in the sponsored listing creation flow
export enum SponsoredListingStep {
  CATEGORY_SELECTION = 0,
  SUBCATEGORIES_AND_GEOS = 1,
  BUDGET_SELECTION = 2,
  SUMMARY = 3,
}

// Define the context state type
type CreateSponsoredListingsState = {
  selectedCampaign: CampaignDtoType | null;
  // Full collections from campaign
  subcategories: SubcategoryType[];
  geos: GeoType[];
  // Selected items (subset of full collections)
  selectedSubcategories: SubcategoryType[];
  selectedGeos: GeoType[];
  budget: string;
  bidStrategy: BidStrategy;
  advancedSettings: AdvancedSettingsState;
  currentStep: SponsoredListingStep;
};

// Define the context actions type
type CreateSponsoredListingsActions = {
  setSelectedCampaign: (campaign: CampaignDtoType) => void;
  // Update full collections
  updateSubcategories: (subcategories: SubcategoryType[]) => void;
  updateGeos: (geos: GeoType[]) => void;
  // Update selected items
  updateSelectedSubcategories: (subcategories: SubcategoryType[]) => void;
  updateSelectedGeos: (geos: GeoType[]) => void;
  updateBudget: (budget: string) => void;
  updateBidStrategy: (bidStrategy: BidStrategy) => void;
  toggleAdvancedSettingsOpen: (isOpen: boolean) => void;
  setAdvancedSettingsEditMode: (
    mode: AdvancedSettingsState['editMode'],
  ) => void;
  updateDelayStartDate: (enabled: boolean, date?: Date | null) => void;
  updateEndDate: (enabled: boolean, date?: Date | null) => void;
  updateManualBidListings: (enabled: boolean, amount?: string) => void;
  updateManualBidRaQ: (enabled: boolean, amount?: string) => void;
  updateListingPacing: (enabled: boolean, value?: string) => void;
  resetState: () => void;
  getPayload: () => SponsoredListingPayload;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  setCurrentStep: (step: SponsoredListingStep) => void;
};

// Create the context with initial undefined values
const CreateSponsoredListingsContext = createContext<
  (CreateSponsoredListingsState & CreateSponsoredListingsActions) | undefined
>(undefined);

// Define action types
enum ActionType {
  SET_SELECTED_CAMPAIGN = 'SET_SELECTED_CAMPAIGN',
  UPDATE_SUBCATEGORIES = 'UPDATE_SUBCATEGORIES',
  UPDATE_GEOS = 'UPDATE_GEOS',
  UPDATE_SELECTED_SUBCATEGORIES = 'UPDATE_SELECTED_SUBCATEGORIES',
  UPDATE_SELECTED_GEOS = 'UPDATE_SELECTED_GEOS',
  UPDATE_BUDGET = 'UPDATE_BUDGET',
  UPDATE_BID_STRATEGY = 'UPDATE_BID_STRATEGY',
  TOGGLE_ADVANCED_SETTINGS_OPEN = 'TOGGLE_ADVANCED_SETTINGS_OPEN',
  SET_ADVANCED_SETTINGS_EDIT_MODE = 'SET_ADVANCED_SETTINGS_EDIT_MODE',
  UPDATE_DELAY_START_DATE = 'UPDATE_DELAY_START_DATE',
  UPDATE_END_DATE = 'UPDATE_END_DATE',
  UPDATE_MANUAL_BID_LISTINGS = 'UPDATE_MANUAL_BID_LISTINGS',
  UPDATE_MANUAL_BID_RAQ = 'UPDATE_MANUAL_BID_RAQ',
  UPDATE_LISTING_PACING = 'UPDATE_LISTING_PACING',
  RESET_STATE = 'RESET_STATE',
  SET_CURRENT_STEP = 'SET_CURRENT_STEP',
}

// Define action interfaces
type Action =
  | { type: ActionType.SET_SELECTED_CAMPAIGN; payload: CampaignDtoType }
  | { type: ActionType.UPDATE_SUBCATEGORIES; payload: SubcategoryType[] }
  | { type: ActionType.UPDATE_GEOS; payload: GeoType[] }
  | {
      type: ActionType.UPDATE_SELECTED_SUBCATEGORIES;
      payload: SubcategoryType[];
    }
  | { type: ActionType.UPDATE_SELECTED_GEOS; payload: GeoType[] }
  | { type: ActionType.UPDATE_BUDGET; payload: string }
  | { type: ActionType.UPDATE_BID_STRATEGY; payload: BidStrategy }
  | { type: ActionType.TOGGLE_ADVANCED_SETTINGS_OPEN; payload: boolean }
  | {
      type: ActionType.SET_ADVANCED_SETTINGS_EDIT_MODE;
      payload: AdvancedSettingsState['editMode'];
    }
  | {
      type: ActionType.UPDATE_DELAY_START_DATE;
      payload: { enabled: boolean; date?: Date | null };
    }
  | {
      type: ActionType.UPDATE_END_DATE;
      payload: { enabled: boolean; date?: Date | null };
    }
  | {
      type: ActionType.UPDATE_MANUAL_BID_LISTINGS;
      payload: { enabled: boolean; amount?: string };
    }
  | {
      type: ActionType.UPDATE_MANUAL_BID_RAQ;
      payload: { enabled: boolean; amount?: string };
    }
  | {
      type: ActionType.UPDATE_LISTING_PACING;
      payload: { enabled: boolean; value?: string };
    }
  | { type: ActionType.RESET_STATE }
  | { type: ActionType.SET_CURRENT_STEP; payload: SponsoredListingStep };

// Initial state
const initialState: CreateSponsoredListingsState = {
  selectedCampaign: null,
  subcategories: [],
  geos: [],
  selectedSubcategories: [],
  selectedGeos: [],
  budget: '200',
  bidStrategy: BidStrategy.LEADER,
  advancedSettings: {
    isOpen: false,
    editMode: null,
    delayStartEnabled: false,
    delayStartDate: null,
    endDateEnabled: false,
    endDate: null,
    manualBidListingsEnabled: false,
    manualBidListingsAmount: '',
    manualBidRaQEnabled: false,
    manualBidRaQAmount: '',
    listingPacingEnabled: false,
    listingPacingValue: '',
  },
  currentStep: SponsoredListingStep.CATEGORY_SELECTION,
};

// Action handlers mapper
type ActionHandler<T extends Action> = (
  state: CreateSponsoredListingsState,
  action: T,
) => CreateSponsoredListingsState;

// Define handlers for each action type
const actionHandlers: {
  [K in ActionType]: ActionHandler<Extract<Action, { type: K }>>;
} = {
  [ActionType.SET_SELECTED_CAMPAIGN]: (state, action) => {
    const campaign = action.payload;

    // Extract subcategories from the campaign
    const subcategories: SubcategoryType[] = (campaign.subCategories || []).map(
      (subCategory) => ({
        id: subCategory.categoryId || '',
        name: subCategory.name || '',
        selected: true,
      }),
    );

    // Extract geographies from the campaign
    const geos: GeoType[] = (campaign.geographies || []).map((geo) => ({
      id: geo.value || '',
      name: geo.name || '',
      postCode: geo.value || '',
      type: geo.type,
      selected: true,
    }));

    // Initially all subcategories and geographies are selected
    const selectedSubcategories = [...subcategories];
    const selectedGeos = [...geos];

    // Removed console.log

    return {
      ...state,
      selectedCampaign: campaign,
      subcategories,
      geos,
      selectedSubcategories,
      selectedGeos,
    };
  },
  [ActionType.UPDATE_SUBCATEGORIES]: (state, action) => ({
    ...state,
    subcategories: action.payload,
  }),
  [ActionType.UPDATE_GEOS]: (state, action) => ({
    ...state,
    geos: action.payload,
  }),
  [ActionType.UPDATE_SELECTED_SUBCATEGORIES]: (state, action) => ({
    ...state,
    selectedSubcategories: action.payload,
  }),
  [ActionType.UPDATE_SELECTED_GEOS]: (state, action) => ({
    ...state,
    selectedGeos: action.payload,
  }),
  [ActionType.UPDATE_BUDGET]: (state, action) => ({
    ...state,
    budget: action.payload,
  }),
  [ActionType.UPDATE_BID_STRATEGY]: (state, action) => ({
    ...state,
    bidStrategy: action.payload,
  }),
  [ActionType.TOGGLE_ADVANCED_SETTINGS_OPEN]: (state, action) => ({
    ...state,
    advancedSettings: {
      ...state.advancedSettings,
      isOpen: action.payload,
    },
  }),
  [ActionType.SET_ADVANCED_SETTINGS_EDIT_MODE]: (state, action) => ({
    ...state,
    advancedSettings: {
      ...state.advancedSettings,
      editMode: action.payload,
    },
  }),
  [ActionType.UPDATE_DELAY_START_DATE]: (state, action) => ({
    ...state,
    advancedSettings: {
      ...state.advancedSettings,
      delayStartEnabled: action.payload.enabled,
      delayStartDate:
        action.payload.date !== undefined ? action.payload.date : null,
    },
  }),
  [ActionType.UPDATE_END_DATE]: (state, action) => ({
    ...state,
    advancedSettings: {
      ...state.advancedSettings,
      endDateEnabled: action.payload.enabled,
      endDate: action.payload.date !== undefined ? action.payload.date : null,
    },
  }),
  [ActionType.UPDATE_MANUAL_BID_LISTINGS]: (state, action) => ({
    ...state,
    advancedSettings: {
      ...state.advancedSettings,
      manualBidListingsEnabled: action.payload.enabled,
      manualBidListingsAmount:
        action.payload.amount !== undefined
          ? action.payload.amount
          : state.advancedSettings.manualBidListingsAmount,
    },
  }),
  [ActionType.UPDATE_MANUAL_BID_RAQ]: (state, action) => ({
    ...state,
    advancedSettings: {
      ...state.advancedSettings,
      manualBidRaQEnabled: action.payload.enabled,
      manualBidRaQAmount:
        action.payload.amount !== undefined
          ? action.payload.amount
          : state.advancedSettings.manualBidRaQAmount,
    },
  }),
  [ActionType.UPDATE_LISTING_PACING]: (state, action) => ({
    ...state,
    advancedSettings: {
      ...state.advancedSettings,
      listingPacingEnabled: action.payload.enabled,
      listingPacingValue:
        action.payload.value !== undefined
          ? action.payload.value
          : state.advancedSettings.listingPacingValue,
    },
  }),
  [ActionType.RESET_STATE]: () => initialState,
  [ActionType.SET_CURRENT_STEP]: (state, action) => ({
    ...state,
    currentStep: action.payload,
  }),
};

// Reducer function using the mapper pattern
function reducer(
  state: CreateSponsoredListingsState,
  action: Action,
): CreateSponsoredListingsState {
  const handler = actionHandlers[action.type];
  // Using a type assertion here as the handler expects a specific action type
  // This is safe because we've already checked that the handler exists for this action type
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return handler ? handler(state, action as any) : state;
}

// Provider component
export const CreateSponsoredListingsProvider: React.FC<{
  children: ReactNode;
}> = ({ children }) => {
  // Use reducer for state management
  const [state, dispatch] = useReducer(reducer, initialState);

  // Actions
  const setSelectedCampaign = useCallback((campaign: CampaignDtoType) => {
    dispatch({ type: ActionType.SET_SELECTED_CAMPAIGN, payload: campaign });
  }, []);

  const updateSubcategories = useCallback(
    (subcategories: SubcategoryType[]) => {
      dispatch({
        type: ActionType.UPDATE_SUBCATEGORIES,
        payload: subcategories,
      });
    },
    [],
  );

  const updateGeos = useCallback((geos: GeoType[]) => {
    dispatch({ type: ActionType.UPDATE_GEOS, payload: geos });
  }, []);

  const updateSelectedSubcategories = useCallback(
    (subcategories: SubcategoryType[]) => {
      dispatch({
        type: ActionType.UPDATE_SELECTED_SUBCATEGORIES,
        payload: subcategories,
      });
    },
    [],
  );

  const updateSelectedGeos = useCallback((geos: GeoType[]) => {
    dispatch({ type: ActionType.UPDATE_SELECTED_GEOS, payload: geos });
  }, []);

  const updateBudget = useCallback((budget: string) => {
    dispatch({ type: ActionType.UPDATE_BUDGET, payload: budget });
  }, []);

  const updateBidStrategy = useCallback((bidStrategy: BidStrategy) => {
    dispatch({ type: ActionType.UPDATE_BID_STRATEGY, payload: bidStrategy });
  }, []);

  const toggleAdvancedSettingsOpen = useCallback((isOpen: boolean) => {
    dispatch({
      type: ActionType.TOGGLE_ADVANCED_SETTINGS_OPEN,
      payload: isOpen,
    });
  }, []);

  const setAdvancedSettingsEditMode = useCallback(
    (mode: AdvancedSettingsState['editMode']) => {
      dispatch({
        type: ActionType.SET_ADVANCED_SETTINGS_EDIT_MODE,
        payload: mode,
      });
    },
    [],
  );

  const updateDelayStartDate = useCallback(
    (enabled: boolean, date?: Date | null) => {
      dispatch({
        type: ActionType.UPDATE_DELAY_START_DATE,
        payload: { enabled, date },
      });
    },
    [],
  );

  const updateEndDate = useCallback((enabled: boolean, date?: Date | null) => {
    dispatch({ type: ActionType.UPDATE_END_DATE, payload: { enabled, date } });
  }, []);

  const updateManualBidListings = useCallback(
    (enabled: boolean, amount?: string) => {
      dispatch({
        type: ActionType.UPDATE_MANUAL_BID_LISTINGS,
        payload: { enabled, amount },
      });
    },
    [],
  );

  const updateManualBidRaQ = useCallback(
    (enabled: boolean, amount?: string) => {
      dispatch({
        type: ActionType.UPDATE_MANUAL_BID_RAQ,
        payload: { enabled, amount },
      });
    },
    [],
  );

  const updateListingPacing = useCallback(
    (enabled: boolean, value?: string) => {
      dispatch({
        type: ActionType.UPDATE_LISTING_PACING,
        payload: { enabled, value },
      });
    },
    [],
  );

  const resetState = useCallback(() => {
    dispatch({ type: ActionType.RESET_STATE });
  }, []);

  // Get the complete payload for submission
  const getPayload = useCallback((): SponsoredListingPayload => {
    const { selectedCampaign, ...rest } = state;
    return {
      ...rest,
      campaignId: selectedCampaign?.campaignId || '',
      subcategories: state.selectedSubcategories,
      geos: state.selectedGeos,
    };
  }, [state]);

  const setCurrentStep = useCallback((step: SponsoredListingStep) => {
    dispatch({ type: ActionType.SET_CURRENT_STEP, payload: step });
  }, []);

  const goToNextStep = useCallback(() => {
    if (state.currentStep < SponsoredListingStep.SUMMARY) {
      dispatch({
        type: ActionType.SET_CURRENT_STEP,
        payload: state.currentStep + 1,
      });
    }
  }, [state.currentStep]);

  const goToPreviousStep = useCallback(() => {
    if (state.currentStep > SponsoredListingStep.CATEGORY_SELECTION) {
      dispatch({
        type: ActionType.SET_CURRENT_STEP,
        payload: state.currentStep - 1,
      });
    }
  }, [state.currentStep]);

  // Combine state and actions
  const value = {
    ...state,
    setSelectedCampaign,
    updateSubcategories,
    updateGeos,
    updateSelectedSubcategories,
    updateSelectedGeos,
    updateBudget,
    updateBidStrategy,
    toggleAdvancedSettingsOpen,
    setAdvancedSettingsEditMode,
    updateDelayStartDate,
    updateEndDate,
    updateManualBidListings,
    updateManualBidRaQ,
    updateListingPacing,
    resetState,
    getPayload,
    goToNextStep,
    goToPreviousStep,
    setCurrentStep,
  };

  return (
    <CreateSponsoredListingsContext.Provider value={value}>
      {children}
    </CreateSponsoredListingsContext.Provider>
  );
};

// Custom hook to use the context
export const useCreateSponsoredListings = (): CreateSponsoredListingsState &
  CreateSponsoredListingsActions => {
  const context = useContext(CreateSponsoredListingsContext);
  if (context === undefined) {
    throw new Error(
      'useCreateSponsoredListings must be used within a CreateSponsoredListingsProvider',
    );
  }

  return context;
};
