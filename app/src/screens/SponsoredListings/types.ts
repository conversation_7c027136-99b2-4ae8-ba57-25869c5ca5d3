import {
  CampaignDtoType,
  GeographyTypeApiEnum,
} from 'src/data/schemas/api/campaigns';
import { BidStrategy } from './context/CreateSponsoredListingsContext';

export interface SubcategoryType {
  id: string | number;
  name: string;
  selected?: boolean;
}

export interface GeoType {
  id: string | number;
  name: string;
  postCode: string;
  type: GeographyTypeApiEnum;
  selected?: boolean;
  count?: number;
}

export interface SponsoredListingPayload {
  campaignId: string | number;
  subcategories?: SubcategoryType[];
  geos?: GeoType[];
  budget?: string;
  bidStrategy?: BidStrategy;
  delayStartDate?: Date | null;
  endDate?: Date | null;
  manualBidListingsEnabled?: boolean;
  manualBidListingsAmount?: string;
  manualBidRaQEnabled?: boolean;
  manualBidRaQAmount?: string;
}

export interface BidLimits {
  sponsoredListings: {
    minBid?: number;
    maxBid?: number;
  };
  requestAQuote: {
    minBid?: number;
    maxBid?: number;
  };
}

export type SponsoredListingsStatusTagProps = {
  isActive: boolean;
  testID?: string;
};

export type SponsoredListingsCardProps = {
  campaign: CampaignDtoType;
  onMenuPress: () => void;
};

export type SponsoredListingsSectionProps = {
  campaigns: CampaignDtoType[];
};

export type GroupedSponsoredListings = {
  [categoryName: string]: CampaignDtoType[];
};

export enum PacingOption {
  EVENLY = 'Evenly',
  ASAP = 'ASAP',
  ACCELERATED = 'Accelerated',
}
