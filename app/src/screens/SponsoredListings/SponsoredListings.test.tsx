import React from 'react';
import { cleanup, render, fireEvent } from '@testing-library/react-native';
import { useNavigation } from '@react-navigation/native';
import { useUserContext } from 'src/hooks/useUser';
import { showToast } from 'src/components';
import { CREATE_SPONSORED_LISTING_SCREEN } from 'src/constants';
import { createQueryClientWrapper } from 'src/utilities/tanstack-query/tanstack-test-utils';
import { SponsoredListings } from './SponsoredListings';
import {
  useGetCampaignStats,
  useGetCampaignStatsForAllPeriods,
} from './hooks/useGetCampaignStats';
import { useSponsoredListingCampaigns } from './hooks/useSponsoredListingCampaigns';

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
  useRoute: jest.fn(),
}));

jest.mock('src/hooks/useUser', () => ({
  useUserContext: jest.fn(),
}));

jest.mock('src/components', () => ({
  ...jest.requireActual('src/components'),
  showToast: jest.fn(),
}));

jest.mock('./hooks/useGetCampaignStats', () => ({
  useGetCampaignStats: jest.fn(),
  useGetCampaignStatsForAllPeriods: jest.fn(),
}));

jest.mock('./hooks/useSponsoredListingCampaigns', () => ({
  useSponsoredListingCampaigns: jest.fn(),
}));

jest.mock('react-native-safe-area-context', () => ({
  SafeAreaView: jest.requireActual('react-native').View,
  useSafeAreaInsets: () => ({ bottom: 0 }),
}));

jest.mock('src/services/datadog/datadog', () => ({
  addAction: jest.fn(),
  captureException: jest.fn(),
}));

jest.mock('src/services/analytics', () => ({
  logEvent: jest.fn(),
}));

describe('SponsoredListings', () => {
  const mockNavigate = jest.fn();
  const defaultStats = [
    {
      impressions: 1000,
      clicks: 100,
      cpc: 2.5,
      clickThroughRate: 10.0,
      spend: 250,
    },
  ];

  const mockCampaigns = [
    {
      campaignId: '123',
      campaignType: 'Ppl',
      category: { categoryId: 1, name: 'Plumbing', isSearchable: true },
      currentBudgetAndBalance: {
        maxBudget: 1000,
        invoiceBalance: 250,
        balance: 250,
        threshold: 800,
        daysRemainingInPeriod: 15,
        leadCount: 5,
        clickCount: 100,
        cumulativeLeadCount: 25,
        budgetBoostAmount: 0,
        isMinimumCommitmentActive: false,
        maxSpend: 1000,
        minimumCommitmentAmount: 0,
        minimumCommitmentUntil: null,
        period: new Date(),
      },
      currentBudget: {
        budgetBoostAmount: 0,
        isMinimumCommitmentActive: false,
        maxBudget: 1000,
        maxSpend: 1000,
        minimumCommitmentAmount: 0,
        minimumCommitmentUntil: null,
        period: new Date(),
      },
      isActive: true,
      isPaused: false,
    },
    {
      campaignId: '456',
      campaignType: 'Fixed',
      category: { categoryId: 2, name: 'Electrical', isSearchable: true },
      currentBudgetAndBalance: {
        maxBudget: 500,
        invoiceBalance: 100,
        balance: 100,
        threshold: 400,
        daysRemainingInPeriod: 20,
        leadCount: 2,
        clickCount: 50,
        cumulativeLeadCount: 10,
        budgetBoostAmount: 0,
        isMinimumCommitmentActive: false,
        maxSpend: 500,
        minimumCommitmentAmount: 0,
        minimumCommitmentUntil: null,
        period: new Date(),
      },
      currentBudget: {
        budgetBoostAmount: 0,
        isMinimumCommitmentActive: false,
        maxBudget: 500,
        maxSpend: 500,
        minimumCommitmentAmount: 0,
        minimumCommitmentUntil: null,
        period: new Date(),
      },
      isActive: true,
      isPaused: false,
    },
  ];

  const mockSponsoredCampaigns = [
    {
      campaignId: '789',
      campaignType: 'MdpSponsoredSearch',
      category: { categoryId: 3, name: 'Heating', isSearchable: true },
      currentBudgetAndBalance: {
        maxBudget: 800,
        invoiceBalance: 200,
        balance: 200,
        threshold: 640,
        daysRemainingInPeriod: 10,
        leadCount: 3,
        clickCount: 75,
        cumulativeLeadCount: 15,
        budgetBoostAmount: 0,
        isMinimumCommitmentActive: false,
        maxSpend: 800,
        minimumCommitmentAmount: 0,
        minimumCommitmentUntil: null,
        period: new Date(),
      },
      currentBudget: {
        budgetBoostAmount: 0,
        isMinimumCommitmentActive: false,
        maxBudget: 800,
        maxSpend: 800,
        minimumCommitmentAmount: 0,
        minimumCommitmentUntil: null,
        period: new Date(),
      },
      isActive: true,
      isPaused: false,
      sponsorship: {
        isSearchSponsored: true,
        isRequestAQuoteSponsored: false,
        searchRateMultiplier: 1.5,
        requestAQuoteRateMultiplier: 1.0,
      },
      ppl: {
        biddingStrategyType: 'FullyAutomatic',
        rateBids: [],
      },
    },
  ];

  beforeEach(() => {
    // Mock console.error to prevent test failures due to React errors
    jest.spyOn(console, 'error').mockImplementation(() => {
      // Intentionally suppress console.error during tests
    });

    // Mock navigation with setOptions method
    (useNavigation as jest.Mock).mockReturnValue({
      navigate: mockNavigate,
      setOptions: jest.fn(),
      canGoBack: jest.fn().mockReturnValue(true),
      goBack: jest.fn(),
      getState: jest.fn().mockReturnValue({
        routes: [],
        index: 0,
      }),
    });

    (useUserContext as jest.Mock).mockReturnValue({ companyId: 123 });
    (useGetCampaignStats as jest.Mock).mockReturnValue({
      stats: defaultStats,
      isLoading: false,
      error: null,
    });
    (useGetCampaignStatsForAllPeriods as jest.Mock).mockReturnValue({
      statsMap: {
        '7d': defaultStats,
        '14d': defaultStats,
        '30d': defaultStats,
        '90d': defaultStats,
        '365d': defaultStats,
      },
      isLoading: false,
      errors: {
        '7d': null,
        '14d': null,
        '30d': null,
        '90d': null,
        '365d': null,
      },
    });
    (useSponsoredListingCampaigns as jest.Mock).mockReturnValue({
      campaigns: mockCampaigns,
      sponsoredCampaigns: mockSponsoredCampaigns,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });
  });

  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it('renders loader while fetching campaign stats', () => {
    (useGetCampaignStatsForAllPeriods as jest.Mock).mockReturnValue({
      statsMap: {},
      isLoading: true,
      errors: {},
    });

    const wrapper = createQueryClientWrapper();
    const { getByTestId } = render(<SponsoredListings />, { wrapper });

    expect(getByTestId('loader')).toBeDefined();
  });

  it('renders loader while fetching campaigns', () => {
    (useSponsoredListingCampaigns as jest.Mock).mockReturnValue({
      campaigns: [],
      isLoading: true,
      error: null,
      refetch: jest.fn(),
    });

    const wrapper = createQueryClientWrapper();
    const { getByTestId } = render(<SponsoredListings />, { wrapper });

    expect(getByTestId('loader')).toBeDefined();
  });

  it('renders create button and performance metrics when data is available', () => {
    const wrapper = createQueryClientWrapper();
    const { getByTestId, getByText } = render(<SponsoredListings />, {
      wrapper,
    });

    expect(getByTestId(SponsoredListings.testIds!.CREATE_BUTTON)).toBeDefined();
    expect(
      getByTestId(SponsoredListings.testIds!.PERFORMANCE_SECTION),
    ).toBeDefined();
    expect(getByText('Your listings performance')).toBeDefined();
  });

  it('shows empty state when no campaigns are available', () => {
    (useSponsoredListingCampaigns as jest.Mock).mockReturnValue({
      campaigns: [],
      sponsoredCampaigns: [],
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    const wrapper = createQueryClientWrapper();
    const { getByTestId } = render(<SponsoredListings />, {
      wrapper,
    });

    expect(getByTestId(SponsoredListings.testIds!.EMPTY_STATE)).toBeDefined();
    // Just check that the empty state is rendered, the exact text may vary
  });

  it('renders sponsored campaign cards when sponsored campaigns are available', () => {
    const wrapper = createQueryClientWrapper();
    const { getByText, getByTestId } = render(<SponsoredListings />, {
      wrapper,
    });

    expect(getByText('Heating')).toBeDefined();
    expect(getByTestId('category-accordion-Heating')).toBeDefined();
  });

  it('renders category headers for sponsored campaigns', () => {
    const wrapper = createQueryClientWrapper();
    const { getByTestId } = render(<SponsoredListings />, {
      wrapper,
    });

    const categoryHeader = getByTestId('category-accordion-Heating');
    expect(categoryHeader).toBeDefined();
  });

  it('navigates to create screen when create button is pressed', () => {
    const wrapper = createQueryClientWrapper();
    const { getByTestId } = render(<SponsoredListings />, { wrapper });

    const createButton = getByTestId(SponsoredListings.testIds!.CREATE_BUTTON);
    fireEvent.press(createButton);

    expect(mockNavigate).toHaveBeenCalledWith(CREATE_SPONSORED_LISTING_SCREEN);
  });

  it('shows error toast when campaign stats fetch fails', () => {
    const error = new Error('Failed to fetch');
    (useGetCampaignStatsForAllPeriods as jest.Mock).mockReturnValue({
      statsMap: {},
      isLoading: false,
      errors: {
        '7d': error,
        '14d': null,
        '30d': null,
        '90d': null,
        '365d': null,
      },
    });

    const wrapper = createQueryClientWrapper();
    render(<SponsoredListings />, { wrapper });

    expect(showToast).toHaveBeenCalledWith({
      text1: 'Unable to retrieve campaigns statistics right now',
      type: 'error',
      position: 'bottom',
      autoHide: true,
    });
  });

  it('verifies performance data is fetched with correct parameters', () => {
    const wrapper = createQueryClientWrapper();
    render(<SponsoredListings />, { wrapper });

    expect(useGetCampaignStatsForAllPeriods).toHaveBeenCalledWith(
      expect.objectContaining({
        dimensions: ['companyId'],
        companyId: 123,
        timePeriods: expect.any(Array),
      }),
    );
  });

  it('initializes with all time periods', () => {
    const wrapper = createQueryClientWrapper();
    render(<SponsoredListings />, { wrapper });

    expect(useGetCampaignStatsForAllPeriods).toHaveBeenCalledWith(
      expect.objectContaining({
        dimensions: ['companyId'],
        companyId: 123,
        timePeriods: expect.any(Array),
      }),
    );
  });
});
