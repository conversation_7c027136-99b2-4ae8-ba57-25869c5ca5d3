import React, { ReactNode } from 'react';
import { cleanup, render } from '@testing-library/react-native';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { Loader } from '@cat-home-experts/react-native-components';
import { SecureContactDetails } from './SecureContactDetails';

const mockUseSecureContactsOptions = jest.fn();
const mockUseAssignedSecureContact = jest.fn();

jest.mock('../../hooks/useSecureContactsOptions', () => ({
  useSecureContactsOptions: () => mockUseSecureContactsOptions(),
}));

jest.mock('../../hooks/useAssignedSecureContact', () => ({
  useAssignedSecureContact: () => mockUseAssignedSecureContact(),
}));

jest.mock('src/hooks/useUser', () => ({
  useUserContext: () => ({ companyId: 1 }),
}));

const wrapper = ({ children }: { children: ReactNode }) => (
  <BottomSheetModalProvider>{children}</BottomSheetModalProvider>
);

describe('SecureContactDetails', () => {
  beforeEach(() => {
    mockUseSecureContactsOptions.mockReturnValue({
      options: { mobileOptions: [{ id: 1, name: 'Alice', number: '12345' }] },
      isLoading: false,
      isError: false,
    });

    mockUseAssignedSecureContact.mockReturnValue({
      assigned: { id: 1, secureNumber: 'Mobile' },
      isLoading: false,
      isError: false,
      isUpdating: false,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    cleanup();
  });

  it('renders dropdown and save button', () => {
    const { getByText } = render(<SecureContactDetails />, { wrapper });
    expect(getByText('Mobile')).toBeTruthy();
    expect(getByText('Save')).toBeTruthy();
  });

  it('shows loader when options are loading', () => {
    mockUseSecureContactsOptions.mockReturnValue({
      options: undefined,
      isLoading: true,
      isError: false,
    });

    const { getByTestId } = render(<SecureContactDetails />, { wrapper });
    expect(getByTestId(Loader.testIds.ROOT)).toBeTruthy();
  });

  it('shows loader when assigned data is loading', () => {
    mockUseAssignedSecureContact.mockReturnValue({
      assigned: undefined,
      isLoading: true,
      isError: false,
      isUpdating: false,
    });

    const { getByTestId } = render(<SecureContactDetails />, { wrapper });
    expect(getByTestId(Loader.testIds.ROOT)).toBeTruthy();
  });
});
