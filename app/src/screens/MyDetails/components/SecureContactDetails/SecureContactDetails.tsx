import React, { useCallback } from 'react';
import { Loader, Typography } from '@cat-home-experts/react-native-components';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { View } from 'react-native';
import { ErrorScreen } from 'src/screens/ErrorScreen';
import { DetailsWrapper } from '../DetailsWrapper';
import { SECTION_SUB_HEADING, SECURE_CONTACTS } from '../../constants';
import { useSecureContactsOptions } from '../../hooks/useSecureContactsOptions';
import { SCDropdown } from './SCDropdown';
import { useAssignedSecureContact } from '../../hooks/useAssignedSecureContact';

export const SecureContactDetails: React.FC = () => {
  const { options, isLoading, isError } = useSecureContactsOptions();
  const {
    assigned,
    updateAssignedSecureContact,
    isLoading: isLoadingAssigned,
    isError: isErrorAssigned,
    isUpdating,
  } = useAssignedSecureContact();

  const handleUpdateAssignedSecureContact = useCallback(
    (contactId: string) => {
      updateAssignedSecureContact(contactId);
    },
    [updateAssignedSecureContact],
  );

  if (isLoading || isLoadingAssigned) {
    return <Loader />;
  }

  if (isError || isErrorAssigned || !assigned || !options) {
    return <ErrorScreen title={SECURE_CONTACTS.errorMessages.fetchData} />;
  }

  return (
    <DetailsWrapper
      sectionSubheading={SECTION_SUB_HEADING.SECURE_CONTACT}
      warningBanner={false}
      sectionDescription={
        <Typography>{SECURE_CONTACTS.description}</Typography>
      }
    >
      <View style={styles.container}>
        <SCDropdown
          options={options.mobileOptions}
          label={assigned.secureNumber}
          defaultSelectedId={assigned.id}
          onSave={handleUpdateAssignedSecureContact}
          isLoading={isUpdating}
        />
      </View>
    </DetailsWrapper>
  );
};

const styles = createMortarStyles(({ spacing }) => ({
  container: {
    paddingVertical: spacing(2),
    gap: spacing(2),
  },
}));
