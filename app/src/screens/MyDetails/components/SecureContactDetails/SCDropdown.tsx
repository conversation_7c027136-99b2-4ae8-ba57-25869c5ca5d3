import React, { useCallback, useMemo, useState } from 'react';
import {
  Dropdown,
  DropdownItemRenderer,
  Typo<PERSON>,
  Button,
} from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import { View } from 'react-native';
import { FormattedOption } from 'src/data/schemas/api/trade-app-bff/secure-contacts/Options';
import { SECURE_CONTACTS } from '../../constants';
import { SCDropdownItem } from './SCDropdownItem';
import { SCDropdownTrigger } from './SCDropdownTrigger';

type SCDropdownProps = {
  options: FormattedOption[];
  onSave: (selectedId: string) => void;
  defaultSelectedId?: string;
  label: string;
  isLoading?: boolean;
};

export const SCDropdown: React.FC<SCDropdownProps> = ({
  options,
  label,
  onSave,
  defaultSelectedId,
  isLoading = false,
}) => {
  const [presented, setPresented] = useState<boolean>(false);
  const [selectedId, setSelectedId] = useState<string | null>(
    defaultSelectedId || null,
  );

  const selected = useMemo(() => {
    if (!selectedId) {
      return null;
    }

    return options.find((option) => option.id === selectedId) || null;
  }, [selectedId, options]);

  const isSaveDisabled = selectedId === defaultSelectedId || !selectedId;

  const handleChange = (selectedItem: FormattedOption) => {
    setPresented(false);
    setSelectedId(selectedItem.id);
  };

  const renderTrigger = useCallback(
    () => <SCDropdownTrigger selected={selected} setPresented={setPresented} />,
    [selected, setPresented],
  );
  const renderItem: DropdownItemRenderer<FormattedOption> = useCallback(
    ({ item }) => {
      const isSelected = isTruthy(selected && selected.id === item.id);
      return (
        <SCDropdownItem
          item={item}
          onChange={handleChange}
          isSelected={isSelected}
        />
      );
    },
    [selected],
  );

  return (
    <>
      <View style={styles.row}>
        <Typography useVariant="bodySemiBold" style={styles.label}>
          {label}
        </Typography>
        <View style={styles.dropdownWrapper}>
          <Dropdown
            value={selected}
            onChange={handleChange}
            options={options}
            presented={presented}
            snapPoints={['75%', '100%']}
            renderItem={renderItem}
            keyGetter={(value) => String(value.id)}
            renderTrigger={renderTrigger}
            onChangePresented={setPresented}
            sheetStyle={styles.dropdown}
          />
        </View>
      </View>
      <Button
        variant="secondary"
        label={SECURE_CONTACTS.labels.save}
        onPress={() => onSave(selectedId!)}
        isDisabled={isSaveDisabled}
        isLoading={isLoading}
        block
      />
    </>
  );
};

const styles = createMortarStyles(({ spacing }) => ({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: spacing(1),
  },
  label: {
    flexShrink: 0,
    flex: 2,
    marginRight: spacing(2),
  },
  dropdownWrapper: {
    flex: 4,
  },
  dropdown: {
    paddingTop: spacing(1),
    paddingBottom: spacing(1),
  },
}));
