import React, { ReactNode } from 'react';
import { cleanup, render } from '@testing-library/react-native';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { Button } from '@cat-home-experts/react-native-components';
import { SCDropdown } from './SCDropdown';

const options = [
  { id: '1', name: '<PERSON>', number: '12345' },
  { id: '2', name: '<PERSON>', number: '67890' },
];

const wrapper = ({ children }: { children: ReactNode }) => (
  <BottomSheetModalProvider>{children}</BottomSheetModalProvider>
);

describe('SCDropdown', () => {
  const mockOnSave = jest.fn();

  afterEach(() => {
    jest.clearAllMocks();
    cleanup();
  });

  it('renders label and dropdown placeholder', () => {
    const { getByText } = render(
      <SCDropdown options={options} label="Mobile" onSave={mockOnSave} />,
      { wrapper },
    );
    expect(getByText('Mobile')).toBeTruthy();
    expect(getByText('Select Secure Contact')).toBeTruthy();
  });

  it('renders save button with default loading state (false)', () => {
    const { getByText } = render(
      <SCDropdown options={options} label="Mobile" onSave={mockOnSave} />,
      { wrapper },
    );
    const saveButton = getByText('Save');
    expect(saveButton).toBeTruthy();
  });

  it('shows loading state on save button when isLoading is true', () => {
    const { getByTestId } = render(
      <SCDropdown
        options={options}
        label="Mobile"
        onSave={mockOnSave}
        isLoading={true}
        defaultSelectedId="1"
      />,
      { wrapper },
    );
    // The Button component with isLoading=true should have a loading indicator
    const saveButtonLoader = getByTestId(Button.testIds.LOADER);
    expect(saveButtonLoader).toBeTruthy();
  });

  it('renders with pre-selected option', () => {
    const { getByText } = render(
      <SCDropdown
        options={options}
        label="Mobile"
        onSave={mockOnSave}
        defaultSelectedId="1"
      />,
      { wrapper },
    );
    expect(getByText('Alice - 12345')).toBeTruthy();
  });
});
