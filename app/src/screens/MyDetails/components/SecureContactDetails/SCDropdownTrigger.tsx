import React from 'react';
import { Icon, Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  palette as staticPalette,
} from '@cat-home-experts/react-native-utilities';
import { TouchableOpacity } from 'react-native';
import { IS_WEB } from 'src/constants';
import { FormattedOption } from 'src/data/schemas/api/trade-app-bff/secure-contacts/Options';
import { SECURE_CONTACTS } from '../../constants';

export const SCDropdownTrigger: React.FC<{
  selected: FormattedOption | null;
  setPresented: (presented: boolean) => void;
}> = ({ selected, setPresented }) => (
  <TouchableOpacity
    onPress={() => setPresented(true)}
    style={styles.triggerRoot}
  >
    <Typography
      useVariant="bodyRegular"
      style={!selected && styles.triggerTextNoValue}
      numberOfLines={1}
      ellipsizeMode="tail"
    >
      {selected
        ? `${selected?.name} - ${selected?.number}`
        : SECURE_CONTACTS.dropdown.placeholder}
    </Typography>
    {IS_WEB && (
      <Icon
        name="chevron-down"
        color={staticPalette.mortar.tokenColorBlueGrey}
        size={20}
      />
    )}
  </TouchableOpacity>
);

const styles = createMortarStyles(({ palette, spacing }) => ({
  triggerTextNoValue: {
    color: palette.mortar.tokenColorBlueGrey,
  },
  triggerRoot: {
    borderWidth: 1,
    gap: spacing(1),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: spacing(6.25),
    borderRadius: spacing(0.5),
    paddingHorizontal: spacing(1.5),
    paddingVertical: spacing(1),
    borderColor: palette.mortar.tokenColorLightGrey,
  },
}));
