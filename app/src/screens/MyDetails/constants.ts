export enum SECTION_SUB_HEADING {
  BUSINESS_DETAILS = 'Business Details',
  CONTACT_DETAILS = 'My Contacts',
  SECURE_CONTACT = 'My Secure Contacts',
}

export const SECTION_DESCRIPTION = {
  contactDetails: {
    edit: 'This is where you can edit the information for all people who are associated with your account.',
    add: ' If you want to add new contacts, visit ',
  },
};

export const MY_TEAM_LINK_TEXT = 'My Team';

export const WARNING_BANNER_TEXT =
  'If you have made a recent change to this information, it needs to be checked by our team. It may take up to 48 hours for any changes to reflect';

export const SECTION = {
  companyName: {
    title: 'Company name',
    description:
      'This is the name of your company as it appears on our website.',
  },
  accountEmail: {
    title: 'Account email address',
    description:
      'This is the primary email address associated with your account.',
  },
  accountPhone: {
    title: 'Account phone number',
    description:
      'This is the primary phone number associated with your account.',
  },
  accountTradingAddress: {
    title: 'Account trading address',
    description: 'This is the address where your business is registered.',
  },
  accountAdminAddress: {
    title: 'Account admin address',
    description:
      'This is an optional secondary address you can associate with your account.',
  },
  contact: {
    description: 'These are the contacts details',
  },
} as const;

const street = { label: 'Street', formFieldName: 'street' } as const;
const city = { label: 'City/Town', formFieldName: 'city' } as const;
const county = { label: 'County', formFieldName: 'county' } as const;
const postcode = { label: 'Postcode', formFieldName: 'postcode' } as const;

const ADDRESS_FIELDS = [street, city, county, postcode] as const;

export const FIELD_NAME_LABEL_MAPPINGS = {
  companyName: {
    label: 'Company name',
    formFieldName: 'companyName',
  },
  accountEmail: {
    label: 'Account email',
    formFieldName: 'memberEmail',
  },
  accountPhone: {
    label: 'Account phone number',
    formFieldName: 'memberPhone',
  },
  accountTradingAddress: ADDRESS_FIELDS,
  accountAdminAddress: ADDRESS_FIELDS,
  contact: {
    companyRole: {
      label: 'Company role',
      formFieldName: 'companyRole',
    },
    firstName: {
      label: 'First name(s)',
      formFieldName: 'firstName',
    },
    lastName: {
      label: 'Last name',
      formFieldName: 'lastName',
    },
    dateOfBirth: {
      label: 'Date of birth',
      formFieldName: 'dateOfBirth',
    },
    email: {
      label: 'Email',
      formFieldName: 'email',
    },
    mobile: {
      label: 'Mobile phone',
      formFieldName: 'mobilePhone',
    },
    phone: {
      label: 'Phone',
      formFieldName: 'phone',
    },
    street: { label: 'Street', formFieldName: 'mailingAddress.street' },
    city: { label: 'City/Town', formFieldName: 'mailingAddress.city' },
    county: { label: 'County', formFieldName: 'mailingAddress.county' },
    postcode: { label: 'Postcode', formFieldName: 'mailingAddress.postcode' },
  },
} as const;

export const FORM_BUTTON_LABELS = { cancel: 'Cancel', submit: 'Submit' };

export const POST_UPDATE_ERROR_MESSAGES = {
  companyName: 'There was an error updating the company name',
  accountEmail: 'There was an error updating the account email address',
  accountPhone: 'There was an error updating the account phone number',
  accountTradingAddress:
    'There was an error updating the account trading address',
  invalidAccountAddress:
    'The provided address was invalid. Please use a real address and try again.',
  accountAdminAddress: 'There was an error updating the account admin address',
  updateContact: 'There was an error updating the contact details',
};

export const COMMON_MODAL_TEXT = {
  reviewDays: '2 working days.',
  changes_not_appear:
    'In the meantime, the changes will not appear in the app.',
};

export const SECURE_CONTACTS = {
  errorMessages: {
    fetchData: 'There was an error grabbing your data, please try again',
  },
  description:
    'This is where you can edit the number we route your secure contact to',
  labels: {
    mobile: 'Mobile',
    save: 'Save',
  },
  dropdown: {
    placeholder: 'Select Secure Contact',
  },
  toast: {
    updateSuccess: 'Secure contact updated',
    updateError: 'Unable to update secure contact, please try again',
  },
} as const;
