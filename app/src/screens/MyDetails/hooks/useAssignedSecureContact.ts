import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { SECURE_CONTACTS_ASSIGNED_QUERY_KEY } from 'src/constants';
import { secureContactsTradeBffApi } from 'src/data/api/trade-app-bff/secure-contacts';
import { Assigned } from 'src/data/schemas/api/trade-app-bff/secure-contacts';
import { useUserContext } from 'src/hooks/useUser';
import { showToast } from 'src/components';
import { SECURE_CONTACTS } from '../constants';

interface useAssignedSecureContactResult {
  assigned: Assigned | undefined;
  updateAssignedSecureContact: (contactId: string) => Promise<void>;
  isLoading: boolean;
  isError: boolean;
  isUpdating: boolean;
}

export const useAssignedSecureContact = (): useAssignedSecureContactResult => {
  const { companyId } = useUserContext();
  const queryClient = useQueryClient();

  if (!companyId) {
    throw new Error(
      'Unable to retrieve account information, there was a problem retrieving the companyId',
    );
  }

  const {
    data: assigned,
    isLoading,
    isError,
  } = useQuery({
    queryKey: [SECURE_CONTACTS_ASSIGNED_QUERY_KEY, companyId],
    queryFn: async () => {
      const response =
        await secureContactsTradeBffApi.getAssignedSecureContact(companyId);
      return response.data;
    },
    enabled: Boolean(companyId),
  });

  const updateSecureContactMutation = useMutation({
    mutationFn: async ({ contactId }: { contactId: string }) => {
      const response =
        await secureContactsTradeBffApi.updateAssignedSecureContact(
          companyId!,
          'SecureMobile1',
          contactId,
        );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [SECURE_CONTACTS_ASSIGNED_QUERY_KEY, companyId],
      });
      showToast({
        type: 'success',
        text1: SECURE_CONTACTS.toast.updateSuccess,
      });
    },
    onError: () => {
      showToast({
        type: 'error',
        text1: SECURE_CONTACTS.toast.updateError,
      });
    },
  });

  const updateAssignedSecureContact = useCallback(
    async (contactId: string) => {
      await updateSecureContactMutation.mutateAsync({
        contactId,
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [updateSecureContactMutation.mutateAsync],
  );

  return {
    assigned,
    updateAssignedSecureContact,
    isLoading,
    isError,
    isUpdating: updateSecureContactMutation.isPending,
  };
};
