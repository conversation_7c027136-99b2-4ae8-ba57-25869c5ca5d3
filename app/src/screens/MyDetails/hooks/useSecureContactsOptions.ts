import { useQuery } from '@tanstack/react-query';
import { SECURE_CONTACTS_OPTIONS_QUERY_KEY } from 'src/constants';
import { secureContactsTradeBffApi } from 'src/data/api/trade-app-bff/secure-contacts';
import { SecureContactsOptionsResponse } from 'src/data/schemas/api/trade-app-bff/secure-contacts/Options';
import { useUserContext } from 'src/hooks/useUser';

interface useSecureContactsOptionsResult {
  options: SecureContactsOptionsResponse | undefined;
  isLoading: boolean;
  isError: boolean;
}

export const useSecureContactsOptions = (): useSecureContactsOptionsResult => {
  const { companyId } = useUserContext();
  if (!companyId) {
    throw new Error(
      'Unable to retrieve account information, there was a problem retrieving the companyId',
    );
  }

  const {
    data: options,
    isLoading,
    isError,
  } = useQuery({
    queryKey: [SECURE_CONTACTS_OPTIONS_QUERY_KEY, companyId],
    queryFn: async () => {
      const response = await secureContactsTradeBffApi.getSecureContactsOptions(
        companyId!,
      ); // query is disabled if companyid is not provided
      return response.data;
    },
    enabled: Boolean(companyId),
  });

  return {
    options,
    isLoading,
    isError,
  };
};
