import { useUserContext } from 'src/hooks/useUser';
import { useQuery } from '@tanstack/react-query';
import { ZUORA_PAYMENT_METHOD_QUERY_KEY } from 'src/screens/DirectDebit/constants';
import { financeTradeBffApi } from 'src/data/api/trade-app-bff/finance';
import { PaymentMethod } from 'src/data/schemas/api/trade-app-bff/finance/PaymentMethod';

type HookReturn = {
  paymentMethod: PaymentMethod | undefined;
  isLoading: boolean;
  isError: boolean;
};

const validateCompanyId = (companyId: number | undefined): number => {
  if (!companyId) {
    throw new Error(
      'Unable to retrieve account information, there was a problem retrieving the companyId',
    );
  }

  return companyId;
};

export function usePaymentMethod(paymentMethodId?: string): HookReturn {
  const { companyId } = useUserContext();

  const {
    data: response,
    isLoading,
    isError,
  } = useQuery({
    queryKey: [ZUORA_PAYMENT_METHOD_QUERY_KEY, companyId, paymentMethodId],
    queryFn: () =>
      financeTradeBffApi.getPaymentMethod(
        paymentMethodId || '',
        validateCompanyId(companyId),
      ),
    enabled: Boolean(companyId && paymentMethodId),
  });

  return {
    paymentMethod: response?.data,
    isLoading,
    isError,
  };
}
