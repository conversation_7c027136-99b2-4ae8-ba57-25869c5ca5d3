import { useUserContext } from 'src/hooks/useUser';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  ZUORA_ACCOUNT_QUERY_KEY,
  ZUORA_PAYMENT_METHOD_QUERY_KEY,
} from 'src/screens/DirectDebit/constants';
import { Account } from 'src/data/schemas/api/trade-app-bff/finance/Account';
import { financeTradeBffApi } from 'src/data/api/trade-app-bff/finance';
import { useCallback } from 'react';

type HookReturn = {
  account: Account | undefined;
  updateAccountDefaultPaymentMethod: (
    accountId: string,
    paymentMethodId: string,
  ) => Promise<void>;
  isLoading: boolean;
  isError: boolean;
};
type patchMutationConfig = {
  accountId: string;
  paymentMethodId: string;
};

const validateCompanyId = (companyId: number | undefined): number => {
  if (!companyId) {
    throw new Error(
      'Unable to retrieve account information, there was a problem retrieving the companyId',
    );
  }

  return companyId;
};

export function useZuoraAccount(): HookReturn {
  const { companyId } = useUserContext();
  const queryClient = useQueryClient();

  const {
    data: response,
    isLoading,
    isError,
  } = useQuery({
    queryKey: [ZUORA_ACCOUNT_QUERY_KEY, companyId],
    queryFn: () => financeTradeBffApi.getAccount(validateCompanyId(companyId)),
    enabled: Boolean(companyId),
  });

  const updateAccountMutation = useMutation({
    mutationFn: async ({ accountId, paymentMethodId }: patchMutationConfig) =>
      financeTradeBffApi
        .updateAccount(validateCompanyId(companyId), accountId, paymentMethodId)
        .then((res) => res.data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ZUORA_ACCOUNT_QUERY_KEY] });
      queryClient.invalidateQueries({
        queryKey: [ZUORA_PAYMENT_METHOD_QUERY_KEY],
      });
    },
  });

  const updateAccountDefaultPaymentMethod = useCallback(
    async (accountId: string, paymentMethodId: string) => {
      await updateAccountMutation.mutateAsync({ accountId, paymentMethodId });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [updateAccountMutation.mutateAsync],
  );

  return {
    account: response?.data,
    updateAccountDefaultPaymentMethod,
    isLoading,
    isError,
  };
}
